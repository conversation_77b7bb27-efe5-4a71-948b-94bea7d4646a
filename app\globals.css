@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
  }
}

@layer components {
  .article-item {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .delay-0 { animation-delay: 0s; }
  .delay-1 { animation-delay: 0.1s; }
  .delay-2 { animation-delay: 0.2s; }
  .delay-3 { animation-delay: 0.3s; }
  .delay-4 { animation-delay: 0.4s; }
  .delay-5 { animation-delay: 0.5s; }
  .delay-6 { animation-delay: 0.6s; }
  .delay-7 { animation-delay: 0.7s; }
  .delay-8 { animation-delay: 0.8s; }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.article-detail {
  @apply bg-white rounded-lg shadow-sm border border-gray-200;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 文章內容樣式 */
.prose {
  @apply max-w-none;
  color: #374151;
  line-height: 1.75;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  @apply text-gray-900 font-semibold;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.25;
}

.prose h1 { @apply text-3xl; }
.prose h2 { @apply text-2xl; }
.prose h3 { @apply text-xl; }
.prose h4 { @apply text-lg; }

.prose p {
  margin-bottom: 1.25em;
}

.prose a {
  @apply text-blue-600 hover:text-blue-700;
  text-decoration: underline;
}

.prose ul, .prose ol {
  margin: 1.25em 0;
  padding-left: 1.5em;
}

.prose li {
  margin: 0.5em 0;
}

/* 代碼塊樣式 */
.prose pre {
  position: relative;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin: 1.5em 0;
  padding: 0;
  overflow: hidden;
}

.prose pre code {
  background: transparent;
  border: none;
  display: block;
  padding: 1.25em;
  color: #1e293b;
  font-size: 0.875em;
  line-height: 1.6;
  font-family: 'Fira Code', ui-monospace, SFMono-Regular, 'SF Mono', Menlo, Consolas, monospace;
  overflow-x: auto;
}

/* 行內代碼樣式 */
.prose :not(pre) > code {
  background: #f1f5f9;
  color: #e11d48;
  border-radius: 4px;
  padding: 0.125em 0.375em;
  font-size: 0.875em;
  font-family: 'Fira Code', ui-monospace, SFMono-Regular, 'SF Mono', Menlo, Consolas, monospace;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

/* 行內代碼 */
.inline-code {
  background: #2d333b;
  color: #adbac7;
  border-radius: 4px;
  padding: 0.1em 0.4em;
  font-size: 0.9em;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Menlo, Consolas, monospace;
  white-space: pre;
  word-break: normal;
  overflow-wrap: normal;
  display: inline;
}

/* 自定義代碼塊右上角複製按鈕 */
.code-copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: #2d333b;
  color: #adbac7;
  border: 1px solid #444c56;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease, background 0.2s ease;
}

.prose pre:hover .code-copy-button {
  opacity: 1;
}

.code-copy-button:hover {
  background: #3c444d;
  color: #ffffff;
}

.code-copy-button.copied {
  background: #347d39;
  color: #ffffff;
  border-color: #46954a;
}

/* 代碼高亮色彩方案 */
.hljs-keyword, .hljs-selector-tag { color: #ff7b72; }
.hljs-string, .hljs-attr { color: #a5d6ff; }
.hljs-function, .hljs-subst { color: #d2a8ff; }
.hljs-number, .hljs-literal { color: #79c0ff; }
.hljs-variable, .hljs-template-variable { color: #ffab70; }
.hljs-comment { color: #8b949e; }

/* 特殊標記樣式，如 `endpoints` 或重要字詞 */
.prose code.highlight {
  background: #fff8c5;
  color: #24292f;
  border: 1px solid #ffe066;
}