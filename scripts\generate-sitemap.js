const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

// 完全自动化的 slugify 函数：处理中文、英文、混合内容（与 lib/tags.ts 保持一致）
function slugify(str) {
  return str
    .normalize('NFKD') // Unicode 标准化
    .replace(/[\u0300-\u036f]/g, '') // 去除重音符号
    .replace(/\s+/g, '-') // 空格转连字符
    .replace(/[_\u00b7\u2022\u2014\u2013]/g, '-') // 常见分隔符转连字符
    .replace(/[^\w\u4e00-\u9fff-]/g, '') // 保留字母、数字、中文、连字符
    .replace(/-+/g, '-') // 合并多个连字符
    .replace(/^-|-$/g, '') // 去除首尾连字符
    .toLowerCase();
}

// 實現 getAllArticles 函數
function getAllArticles() {
  try {
    const articlesDirectory = path.join(process.cwd(), 'data/articles');
    if (!fs.existsSync(articlesDirectory)) {
      console.warn('警告：articles 目錄不存在：', articlesDirectory);
      return [];
    }

    const fileNames = fs.readdirSync(articlesDirectory);
    const allArticlesData = fileNames
      .filter(fileName => fileName.endsWith('.md'))
      .map(fileName => {
        // 移除 ".md" 取得檔名作為 slug
        const slug = fileName.replace(/\.md$/, '');

        // 讀取 markdown 檔案內容
        const fullPath = path.join(articlesDirectory, fileName);
        const fileContents = fs.readFileSync(fullPath, 'utf8');

        // 使用 gray-matter 解析 metadata
        const matterResult = matter(fileContents);

        // 合併資料與 slug
        return {
          slug,
          ...matterResult.data,
          content: matterResult.content
        };
      });

    // 依 article_id 降序排序
    return allArticlesData.sort((a, b) => {
      return (Number(b.article_id) || 0) - (Number(a.article_id) || 0);
    });
  } catch (error) {
    console.error('取得文章列表時發生錯誤：', error);
    return [];
  }
}

const domain = 'https://learnmarts.com';

function generateSitemap() {
  try {
    const articles = getAllArticles();

    // 獲取所有分類和標籤
    const categories = [...new Set(articles.map(article => slugify(article.category)).filter(Boolean))];
    const allTags = articles.flatMap(article => article.tags || []).map(tag => slugify(tag));
    const tags = [...new Set(allTags)];

    // 產生 sitemap.xml
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${domain}</loc>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${domain}/articles</loc>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>${domain}/categories</loc>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${domain}/tags</loc>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${domain}/about</loc>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>${domain}/contact</loc>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>${domain}/privacy</loc>
    <changefreq>yearly</changefreq>
    <priority>0.5</priority>
  </url>
  <url>
    <loc>${domain}/terms</loc>
    <changefreq>yearly</changefreq>
    <priority>0.5</priority>
  </url>
  ${articles.map(article => `
  <url>
    <loc>${domain}/articles/${article.slug}</loc>
    <lastmod>${new Date(article.date).toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  `).join('')}
  ${categories.map(category => `
  <url>
    <loc>${domain}/categories/${slugify(category)}</loc>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
  `).join('')}
  ${tags.map(tag => `
  <url>
    <loc>${domain}/tags/${slugify(tag)}</loc>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
  `).join('')}
  ${tags.map(tag => slugify(tag)).join(', ')}
</urlset>`;

    // 產生增強版 llms.txt
    const llmsContent = `# llms.txt for learnmarts.com
# 本檔案為大型語言模型提供如何互動本站內容的指引

# 允許 LLMs 使用本站內容
Allow: /

# 首選網站描述
SiteDescription: Anmody AI 教學部落由 Anmody.com 維護，專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學文章，助您掌握最新 AI 應用技術與開發技巧。

# 內容來源與引用偏好
ContentSource: https://learnmarts.com/
CitationPreference: include-url

# 允許內容摘要
AllowSummarization: True

# 允許內容引用
AllowQuotation: True

# 引用時首選網址結構
PreferredCitationURL: https://learnmarts.com/articles/{slug}

# 內容更新頻率
ContentUpdateFrequency: Weekly

# LLMs 用的中繼資料
ContentOwner: Anmody.com
ContentTopics: OpenAI, ChatGPT, AI教學, 部落格, API開發, 人工智慧, 機器學習, 程式教學
ContentLanguage: zh-HK

# 網站結構與內容索引
# 主要頁面：
ContentUrl: ${domain}/
ContentType: BlogHomePage
ContentDescription: Anmody AI 教學部落首頁，展示最新的 AI 技術教學文章。

ContentUrl: ${domain}/articles
ContentType: ArticleListPage
ContentDescription: 所有 AI 技術教學文章列表。

ContentUrl: ${domain}/categories
ContentType: CategoryListPage
ContentDescription: 文章分類頁面。

ContentUrl: ${domain}/tags
ContentType: TagListPage
ContentDescription: 文章標籤頁面。

# 文章頁面：
${articles.map(article => `
ContentUrl: ${domain}/articles/${article.slug}
ContentType: BlogPost
ContentTitle: ${article.title}
ContentDate: ${article.date}
ContentDescription: ${article.excerpt}
ContentCategory: ${article.category || 'General'}
ContentTags: ${(article.tags || []).join(', ')}
`).join('')}
`;

    const publicDir = path.join(process.cwd(), 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir);
    }

    fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), sitemap);
    fs.writeFileSync(path.join(publicDir, 'llms.txt'), llmsContent);
    
    console.log('✅ Sitemap 與增強版 llms.txt 已產生！');
    console.log(`📄 包含 ${articles.length} 篇文章`);
    console.log(`📂 包含 ${categories.length} 個分類`);
    console.log(`🏷️ 包含 ${tags.length} 個標籤`);
  } catch (error) {
    console.error('產生 sitemap 時發生錯誤：', error);
    process.exit(1);
  }
}

generateSitemap();
