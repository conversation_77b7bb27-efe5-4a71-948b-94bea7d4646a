import ArticleCard from '@/components/ArticleCard'
import { getAllArticles } from '@/lib/articles'
import type { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '所有文章 | Anmody AI 教學部落',
  description: '瀏覽所有 AI 技術教學文章，包含 OpenAI API、ChatGPT 應用開發等豐富內容。',
}

export default function ArticlesPage() {
  const articles = getAllArticles()
  
  // 獲取所有分類
  const categories = Array.from(
    new Set(articles.map(article => article.category).filter(Boolean))
  ) as string[]

  return (
    <div className="max-w-6xl mx-auto">
      {/* Page Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">所有文章</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          探索我們的 AI 技術教學文章，掌握最新的人工智慧應用技術
        </p>
      </div>

      {/* Categories Filter */}
      {categories.length > 0 && (
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">按分類瀏覽</h2>
          <div className="flex flex-wrap gap-3">
            <Link 
              href="/articles" 
              className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              全部文章 ({articles.length})
            </Link>
            {categories.map((category) => {
              const count = articles.filter(article => article.category === category).length
              return (
                <Link
                  key={category}
                  href={`/categories/${encodeURIComponent(category)}`}
                  className="inline-block bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  {category} ({count})
                </Link>
              )
            })}
          </div>
        </div>
      )}

      {/* Articles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {articles.map((article, index) => (
          <div
            key={article.slug}
            className={`transform hover:-translate-y-1 transition-all duration-300 article-item delay-${index % 6}`}
          >
            <ArticleCard {...article} />
          </div>
        ))}
      </div>

      {articles.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">暫無文章</p>
        </div>
      )}
    </div>
  )
}
