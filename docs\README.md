# 📚 Anmody AI 教学博客 - 文档中心

欢迎来到 Anmody AI 教学博客的文档中心！这里包含了创作者和维护者需要的所有指南和资源。

## 📖 文档目录

### 🎯 创作者指南

| 文档 | 描述 | 适用人群 |
|------|------|----------|
| [📝 创建文章的说明](./创建文章的说明.md) | 完整的文章创作指南 | 新手创作者 |
| [⚡ 文章创作快速参考](./文章创作快速参考.md) | 快速参考手册 | 有经验的创作者 |
| [📄 文章模板](./文章模板.md) | 标准文章模板 | 所有创作者 |

### 🛠️ 工具和脚本

| 工具 | 命令 | 描述 |
|------|------|------|
| 新文章创建工具 | `npm run new-article` | 交互式创建新文章 |
| 文章数据生成 | `npm run generate-articles-data` | 生成文章索引数据 |
| RSS 生成 | `npm run generate-rss` | 生成 RSS 订阅源 |
| 站点地图生成 | `npm run generate-sitemap` | 生成搜索引擎站点地图 |

## 🚀 快速开始

### 创建新文章（推荐方式）

```bash
# 使用交互式工具创建新文章
npm run new-article

# 按照提示填写文章信息
# 工具会自动生成文章文件和必要的元数据
```

### 手动创建文章

```bash
# 1. 复制模板文件
cp docs/文章模板.md data/articles/your-article-name.md

# 2. 编辑文章内容
# 3. 构建项目
npm run build

# 4. 本地预览
npx serve@latest out
```

## 📋 文章创作流程

### 1. 准备阶段
- [ ] 确定文章主题和目标读者
- [ ] 查看现有文章，避免重复内容
- [ ] 准备必要的代码示例和资源

### 2. 创作阶段
- [ ] 使用 `npm run new-article` 创建文章框架
- [ ] 按照模板结构编写内容
- [ ] 添加代码示例和配置文件
- [ ] 包含故障排除和最佳实践

### 3. 审核阶段
- [ ] 检查文章格式和语法
- [ ] 验证所有代码示例可运行
- [ ] 确保链接有效
- [ ] 本地构建测试

### 4. 发布阶段
- [ ] 提交代码到版本控制
- [ ] 部署到生产环境
- [ ] 验证文章正常显示

## 🎯 内容策略

### 热门主题方向

1. **AI 技术应用**
   - OpenAI API 使用技巧
   - ChatGPT 集成方案
   - AI 工具评测和对比

2. **现代开发技术**
   - 容器化部署（Docker, Kubernetes）
   - 云原生架构
   - DevOps 实践和自动化

3. **前端开发**
   - React/Next.js 最佳实践
   - 现代 JavaScript 技术
   - 性能优化技巧

4. **后端开发**
   - API 设计和开发
   - 数据库优化
   - 微服务架构

### 文章类型建议

- **📚 入门教程**：面向初学者的基础指南
- **🔧 进阶实践**：深入的技术实现和优化
- **🚨 问题解决**：常见问题的排查和解决
- **📊 工具评测**：新技术和工具的评估
- **💡 最佳实践**：经验总结和建议

## 📊 项目统计

### 当前状态
- **文章总数**：8 篇
- **分类数量**：3 个
- **标签数量**：18 个
- **静态页面**：85 个

### 分类分布
- **教學** (tutorials)：6 篇
- **用戶指南** (user-guide-cat)：1 篇
- **站務公告** (site-announcements)：1 篇

## 🔧 技术架构

### 核心技术栈
- **Next.js 14** - React 框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Markdown** - 内容格式

### 构建流程
1. **数据生成** - 从 Markdown 文件生成 JSON 数据
2. **RSS 生成** - 创建 RSS 订阅源
3. **站点地图** - 生成搜索引擎站点地图
4. **静态构建** - Next.js 静态导出

### 部署平台
- **Cloudflare Pages** - 静态网站托管
- **自动部署** - Git 推送触发构建

## 📈 SEO 优化

### 已实现的优化
- ✅ 完整的元数据配置
- ✅ OpenGraph 和 Twitter Cards
- ✅ 结构化数据 (JSON-LD)
- ✅ 自动生成站点地图
- ✅ RSS 订阅源
- ✅ 语义化 HTML 结构

### 内容 SEO 建议
- 标题包含目标关键词
- 摘要控制在 150-160 字符
- 使用清晰的标题层次 (H1-H3)
- 内部链接优化
- 图片添加 alt 属性

## 🚨 常见问题

### 构建错误
**问题**：`npm run build` 失败
**解决**：
1. 检查文章 front matter 格式
2. 确保 article_id 唯一
3. 验证 YAML 语法

### 文章不显示
**问题**：新文章不在网站上显示
**解决**：
1. 检查文件路径是否正确
2. 重新运行 `npm run build`
3. 清除浏览器缓存

### 分类/标签问题
**问题**：文章不在正确的分类下
**解决**：
1. 检查 category 和 category_slug 匹配
2. 确认分类名称拼写正确
3. 重新生成文章数据

## 📞 获取帮助

### 技术支持
- **文档问题**：查看本文档中心
- **构建错误**：检查终端错误信息
- **格式问题**：参考现有文章格式

### 联系方式
- **GitHub Issues**：技术问题和 bug 报告
- **网站反馈**：通过联系表单提交建议
- **紧急问题**：联系项目维护者

## 🎉 贡献指南

我们欢迎所有形式的贡献：

1. **内容贡献**
   - 撰写新文章
   - 改进现有内容
   - 翻译文章

2. **技术贡献**
   - 修复 bug
   - 添加新功能
   - 优化性能

3. **文档贡献**
   - 完善文档
   - 添加示例
   - 改进指南

---

## 📝 更新日志

### 2025-07-01
- ✅ 创建完整的文档体系
- ✅ 添加交互式文章创建工具
- ✅ 提供标准化的文章模板
- ✅ 建立内容创作流程

---

**感谢您为 Anmody AI 教学博客的贡献！** 🙏

*如有任何问题或建议，请随时联系我们。*
