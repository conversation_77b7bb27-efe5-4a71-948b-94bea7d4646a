# OpenAI API 使用教學

[https://learnmarts.com](https://learnmarts.com)

## 發佈方式：

```bash
$ git add .
$ git commit -m "update"
$ git push -u origin main
```

```
wsl git add . && git commit -m "update" && git push -u origin main
```

## 在 Cloudflare Workers Pages 部署時，請選擇以下設定：

```
框架預設
Next.js（Static HTML Export）

建置指令
npx next build

建置輸出目錄
out

```

## 若您希望修改首頁佈局，請注意以下檔案：

app/page.tsx - 這是首頁的主要元件檔案，包含首頁的基本結構、標題與公告卡片列表的渲染邏輯。

components/AnnouncementCard.tsx - 此元件負責渲染每則公告的卡片樣式，修改此檔案會影響首頁上每則公告的顯示方式。

app/globals.css - 若需新增全域樣式或修改現有樣式，請編輯此檔案，內含全域 CSS 定義。

tailwind.config.js - 若需調整 Tailwind CSS 設定（如新增自訂顏色、間距等），請修改此檔案。

components/SpaceBackground.tsx - 若想修改頁面背景效果，此元件控制背景樣式。

其中，app/page.tsx 是最關鍵的檔案，因為它定義了首頁的整體結構與佈局。您可以更改其中的 HTML 結構、CSS 類別或元件組合方式，以實現不同的佈局效果.