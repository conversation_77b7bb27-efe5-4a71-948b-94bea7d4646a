---
title: Android 工程常用術語
date: 2025-05-17T00:00:00.000Z
excerpt: 收錄 Android 工程開發中最常見的專業術語與解釋，助你快速掌握專案結構、開發流程與調試技巧。
category: 教學
tags:
  - Android
article_id: 6
tags_slug:
  - android
category_slug: tutorials
---

# Android 工程常用術語

## 1. 基礎術語
- **包（Package）**：Java/Kotlin 程式碼的組織單元，用於命名空間管理。
- **類（Class）**：定義物件的藍圖，包含屬性和方法。
- **方法（Method）**：類中定義的具體功能或行為。

## 2. 專案結構相關
- **模組（Module）**：獨立建構單元，如 app 模組、函式庫模組。
- **Activity**：用戶介面畫面。
- **Fragment**：Activity 的子介面，模組化 UI。
- **Service**：背景任務元件，無 UI。
- **BroadcastReceiver**：接收廣播訊息。
- **ContentProvider**：跨應用程式共享資料。
- **Application**：應用程式全域進入點類別。
- **Gradle**：建構工具，管理相依。
- **Manifest（AndroidManifest.xml）**：應用程式設定檔。
- **Resource（資源）**：版面、字串、圖片、主題等。
- **AAR**：Android 函式庫檔案格式。
- **APK**：應用程式安裝包。
- **AAB（Android App Bundle）**：動態交付發佈格式。

## 3. 程式碼與開發相關
- **介面（Interface）**：方法契約。
- **抽象類（Abstract Class）**：定義通用行為。
- **註解（Annotation）**：如 `@Override`、`@Nullable`。
- **回呼（Callback）**：非同步結果處理。
- **Intent**：元件間通訊。
- **Bundle**：傳遞資料的鍵值對。
- **View**：UI 元素，如 Button。
- **ViewModel**：管理 UI 資料，生命週期感知。
- **LiveData**：可觀察資料，生命週期感知。
- **DataBinding**：UI 與資料綁定。
- **Room**：本地資料庫函式庫。
- **Coroutine**：Kotlin 非同步框架。
- **Flow**：Kotlin 響應式資料流。

## 4. 架構與設計相關
- **MVC/MVP/MVVM**：架構模式。
- **Repository**：統一管理資料來源。
- **Dependency Injection（依賴注入）**：如 Dagger、Hilt。
- **Jetpack**：Android 開發函式庫集合。
- **Lifecycle**：管理元件生命週期。
- **WorkManager**：排程背景任務。
- **Navigation**：Fragment 導航。

## 5. 除錯與建構相關
- **Logcat**：日誌工具。
- **Debugger**：中斷點除錯。
- **Lint**：靜態程式碼分析。
- **ProGuard/R8**：程式碼混淆最佳化。
- **Build Variant**：建構變體，如 debug、release。
- **Flavor**：產品變體，如免費版、付費版。
- **Keystore**：簽章金鑰。

## 6. 其他常見術語
- **Context**：提供應用程式環境資訊。
- **Permissions（權限）**：如儲存、相機。
- **Thread（執行緒）**：主執行緒、副執行緒。
- **Handler**：執行緒間通訊。
- **Looper**：訊息迴圈。
- **AsyncTask**：非同步任務（已淘汰）。
- **SharedPreferences**：鍵值對儲存。
- **Parcel**：序列化資料容器。
- **ART**：Android 執行時。
- **Dalvik**：早期虛擬機（已淘汰）。
