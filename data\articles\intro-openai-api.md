---
title: 初步認識 OpenAI API
date: 2025-04-22T00:00:00.000Z
excerpt: 用最簡單嘅方式，帶你了解 OpenAI API 嘅基本名詞同使用原理。
category: 教學
tags:
  - OpenAI
  - API
article_id: 2
tags_slug:
  - openai
  - api
category_slug: tutorials
---

## 乜嘢係 OpenAI API？

OpenAI API 係由 OpenAI 提供嘅一組網絡介面（API），等你可以用程式方式連接同使用 OpenAI 嘅人工智能服務，例如 ChatGPT、GPT-4o、o3、o4、DALL·E 等。

## 常見名詞解釋

- **API KEY**  
  API KEY 就係你用嚟認證身份嘅密鑰，好似你嘅會員證一樣。每次用 API 時都要帶住呢個 KEY，OpenAI 先會識得你係邊個同埋計算用量。

- **BASE_URL**  
  BASE_URL 係 API 嘅主網址，所有請求都要發去呢個網址。例如官方 API 嘅 BASE_URL 係 `https://api.openai.com/v1/`，如果你用代理或者第三方服務，BASE_URL 可能會唔同。

- **Endpoint**  
  Endpoint 係 API 入面唔同功能嘅路徑，例如 `/chat/completions` 係用嚟同 ChatGPT 對話嘅 endpoint。依家仲有一個新嘅路徑 `/v1/responses` ，兩者大致相同，而目前官方建議使用後者。

- **Token**  
  Token 係計算你輸入同輸出文字長度嘅單位，API 收費都係根據 token 數量計算。

## OpenAI API 使用原理

1. 你可以向我哋購買 API 額度，由我們創建 API KEY 畀你或你自己創建一個 API KEY。
2. 用你嘅 API KEY 同 BASE_URL，喺程式（例如 Python、JavaScript）入面發送請求（request）去 OpenAI API。
3. API 會根據你嘅請求（例如輸入一段文字），返回對應嘅 AI 回應。
4. 每次請求都會消耗 token，OpenAI 會根據 token 數量收費。

## 小貼士

- API KEY 千祈唔好公開畀人睇，否則有機會被濫用。
- 如果你用第三方服務，記得確認 BASE_URL 同 API KEY 嘅安全性。
- 建議新手可以用 Postman 或 curl 測試 API，熟悉流程。

希望呢篇文章可以幫你打好 OpenAI API 嘅基礎，有咩問題歡迎留言或聯絡我哋！

Contact us at:

[https://t.afffun.com/cmvp88bt](https://t.afffun.com/cmvp88bt)
