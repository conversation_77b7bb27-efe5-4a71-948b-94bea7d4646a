import type { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '聯絡我們 | Anmody AI 教學部落',
  description: '聯絡 Anmody AI 教學部落團隊，獲取技術支援、業務合作或提供寶貴建議。',
}

export default function ContactPage() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">聯絡我們</h1>
        <p className="text-xl text-gray-600">
          我們很樂意聽到您的聲音，歡迎與我們聯絡
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Contact Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">聯絡方式</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">業務合作</h3>
              <p className="text-gray-600 mb-2">
                如果您需要購買 API KEY，或者有合作想法、商業洽談：
              </p>
              <Link 
                href="https://t.afffun.com/cmvp88bt" 
                className="text-blue-600 hover:text-blue-700 font-medium"
                target="_blank"
                rel="noopener noreferrer"
              >
                https://t.afffun.com/cmvp88bt
              </Link>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">服務購買</h3>
              <p className="text-gray-600 mb-2">
                訂閱 ChatGPT Plus Share Account Plan，只需 HK$85/月：
              </p>
              <Link 
                href="https://www.anmody.com" 
                className="text-blue-600 hover:text-blue-700 font-medium"
                target="_blank"
                rel="noopener noreferrer"
              >
                https://www.anmody.com
              </Link>
              <p className="text-sm text-gray-500 mt-1">
                24小時自動運作，隨時可購買
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">技術支援</h3>
              <p className="text-gray-600">
                如果您在使用我們的教學內容時遇到問題，或者有技術相關的疑問，歡迎透過上述聯絡方式與我們聯繫。
              </p>
            </div>
          </div>
        </div>

        {/* FAQ */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">常見問題</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">如何獲得 OpenAI API Key？</h3>
              <p className="text-gray-600">
                您可以透過我們的業務合作聯絡方式購買 API Key，我們提供穩定可靠的服務。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">是否提供技術諮詢服務？</h3>
              <p className="text-gray-600">
                是的，我們提供 AI 技術相關的諮詢服務。請透過聯絡方式與我們討論具體需求。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">可以申請文章投稿嗎？</h3>
              <p className="text-gray-600">
                我們歡迎高質量的技術文章投稿。請先透過聯絡方式與我們討論投稿事宜。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">回覆時間</h3>
              <p className="text-gray-600">
                我們通常會在 24-48 小時內回覆您的訊息。緊急事務請在訊息中註明。
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white text-center">
        <h2 className="text-2xl font-bold mb-4">準備開始您的 AI 學習之旅？</h2>
        <p className="text-blue-100 mb-6">
          瀏覽我們的教學文章，掌握最新的 AI 技術
        </p>
        <Link 
          href="/articles" 
          className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
        >
          開始學習
        </Link>
      </div>
    </div>
  )
}
