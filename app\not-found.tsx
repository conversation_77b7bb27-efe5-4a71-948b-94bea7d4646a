import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-200">404</h1>
          <div className="text-4xl font-bold text-gray-900 mb-4">頁面未找到</div>
          <p className="text-xl text-gray-600 mb-8">
            抱歉，您訪問的頁面不存在或已被移動。
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/" 
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              回到首頁
            </Link>
            <Link 
              href="/articles" 
              className="inline-block bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
            >
              瀏覽文章
            </Link>
          </div>
          
          <div className="mt-8">
            <p className="text-gray-500 mb-4">您可能在尋找：</p>
            <div className="flex flex-wrap justify-center gap-2">
              <Link href="/categories" className="text-blue-600 hover:text-blue-700 text-sm">
                文章分類
              </Link>
              <span className="text-gray-300">|</span>
              <Link href="/tags" className="text-blue-600 hover:text-blue-700 text-sm">
                標籤
              </Link>
              <span className="text-gray-300">|</span>
              <Link href="/about" className="text-blue-600 hover:text-blue-700 text-sm">
                關於我們
              </Link>
              <span className="text-gray-300">|</span>
              <Link href="/contact" className="text-blue-600 hover:text-blue-700 text-sm">
                聯絡我們
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
