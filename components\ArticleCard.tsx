"use client";
import Link from 'next/link'
import { Article } from '@/lib/articles'
import { slugifyTag, slugifyCategory } from '@/lib/tags'

type ArticleCardProps = Omit<Article, 'content'> & {
  featured?: boolean
}

export default function ArticleCard({
  slug,
  title,
  excerpt,
  date,
  category,
  tags,
  author,
  readTime,
  featured = false
}: ArticleCardProps) {
  const formattedDate = date instanceof Date ?
    date.toLocaleDateString('zh-HK') :
    date

  const cardClasses = featured
    ? "bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 group"
    : "bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300 group"

  return (
    <article className={cardClasses}>
      <Link href={`/articles/${slug}`} className="block">
        {featured && (
          <div className="h-48 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
            <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            <div className="absolute bottom-4 left-4 right-4">
              <h2 className="text-xl font-bold text-white mb-2 line-clamp-2">
                {title}
              </h2>
            </div>
          </div>
        )}

        <div className={featured ? "p-6" : ""}>
          {!featured && (
            <h2 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
              {title}
            </h2>
          )}

          <div className="flex flex-wrap items-center gap-2 mb-3">
            {category && (
              <Link
                href={`/categories/${slugifyCategory(category)}`}
                className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium"
                onClick={e => e.stopPropagation()}
              >
                {category}
              </Link>
            )}
            {tags && tags.slice(0, 3).map(tag => (
              <Link 
                key={tag} 
                href={`/tags/${slugifyTag(tag)}`}
                className="inline-block bg-gray-100 hover:bg-blue-100 text-gray-600 hover:text-blue-700 text-xs px-2 py-1 rounded-full transition-colors"
                onClick={(e) => e.stopPropagation()}
              >
                #{tag}
              </Link>
            ))}
          </div>

          <p className="text-gray-600 mb-4 line-clamp-3 leading-relaxed">
            {excerpt}
          </p>

          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span>作者：{author}</span>
              {readTime && <span>{readTime}</span>}
            </div>
            <time>{formattedDate}</time>
          </div>
        </div>
      </Link>
    </article>
  )
}