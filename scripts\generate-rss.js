const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

function generateRSSFeed() {
  const articlesDirectory = path.join(process.cwd(), 'data/articles');
  const fileNames = fs.readdirSync(articlesDirectory);
  
  const articles = fileNames
    .filter(name => name.endsWith('.md'))
    .map(name => {
      const fullPath = path.join(articlesDirectory, name);
      const fileContents = fs.readFileSync(fullPath, 'utf8');
      const { data, content } = matter(fileContents);
      
      return {
        slug: name.replace(/\.md$/, ''),
        title: data.title,
        description: data.excerpt || content.substring(0, 200) + '...',
        date: data.date,
        category: data.category,
        content: content,
        author: data.author || 'Anmody',
        tags: data.tags || []
      };
    })
    .sort((a, b) => new Date(b.date) - new Date(a.date))
    .slice(0, 20); // 最新20篇文章

  // 中文词组到英文 slug 的映射（与 lib/tags.ts 保持一致）
  function chineseToSlug(text) {
    const chineseSlugMap = {
      '快速入門': 'quick-start',
      '使用指南': 'user-guide', 
      '安裝指南': 'installation-guide',
      '對話管理': 'conversation-management',
      '站務': 'site-management',
      '開發者模式': 'developer-mode',
      '關於': 'about',
      'Chrome 擴展': 'chrome-extension',
      'ZIP 安裝': 'zip-installation',
      '教學': 'tutorials',
      '用戶指南': 'user-guide-cat',
      '站務公告': 'site-announcements',
      'ChatGPT': 'chatgpt',
      'Plus': 'plus',
      'Android': 'android',
      'OpenAI': 'openai',
      'API': 'api'
    };
    
    // 直接查找完整匹配
    if (chineseSlugMap[text]) {
      return chineseSlugMap[text];
    }
    
    // 如果没有找到映射，保持原文（用于英文标签）
    return text;
  }

  // 改进的 slugify 函数：优先使用完整词组映射，避免字符级别处理
  function slugify(str) {
    // 首先尝试直接映射整个字符串
    const directMapping = chineseToSlug(str);
    if (directMapping !== str) {
      return directMapping;
    }
    
    // 对于英文或混合内容，进行标准化处理
    return str
      .normalize('NFKD')
      .replace(/[\u0300-\u036f]/g, '') // 去除重音符号
      .replace(/\s+/g, '-') // 空格转 -
      .replace(/[_\u00b7\u2022\u2014\u2013]/g, '-') // 常见分隔符转 -
      .replace(/[^\w-]/g, '') // 只保留字母、数字、-
      .replace(/-+/g, '-') // 合并多个连字符
      .replace(/^-|-$/g, '') // 去除首尾连字符
      .toLowerCase();
  }

  const rssItems = articles.map(article => `
    <item>
      <title><![CDATA[${article.title}]]></title>
      <description><![CDATA[${article.description}]]></description>
      <link>https://learnmarts.com/articles/${article.slug}</link>
      <guid>https://learnmarts.com/articles/${article.slug}</guid>
      <pubDate>${new Date(article.date).toUTCString()}</pubDate>
      <category><![CDATA[${slugify(article.category)}]]></category>
      ${article.tags.map(tag => `<category><![CDATA[${slugify(tag)}]]></category>`).join('\n')}
      ${article.tags.map(tag => slugify(tag)).join(', ')}
      <author><EMAIL> (${article.author})</author>
    </item>`).join('');

  const rssContent = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:content="http://purl.org/rss/1.0/modules/content/" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Anmody AI 教學部落</title>
    <description>專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學文章，助您掌握最新 AI 應用技術與開發技巧。</description>
    <link>https://learnmarts.com</link>
    <atom:link href="https://learnmarts.com/rss.xml" rel="self" type="application/rss+xml"/>
    <language>zh-Hant-HK</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <managingEditor><EMAIL> (Anmody)</managingEditor>
    <webMaster><EMAIL> (Anmody)</webMaster>
    <category>Technology</category>
    <category>AI</category>
    <category>Education</category>
    <ttl>60</ttl>
    <image>
      <url>https://learnmarts.com/favicon.ico</url>
      <title>Anmody AI 教學部落</title>
      <link>https://learnmarts.com</link>
    </image>
    ${rssItems}
  </channel>
</rss>`;

  const publicDir = path.join(process.cwd(), 'public');
  fs.writeFileSync(path.join(publicDir, 'rss.xml'), rssContent);
  console.log('✅ RSS feed generated!');
}

if (require.main === module) {
  generateRSSFeed();
}

module.exports = generateRSSFeed;
