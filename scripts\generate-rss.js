const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

function generateRSSFeed() {
  const articlesDirectory = path.join(process.cwd(), 'data/articles');
  const fileNames = fs.readdirSync(articlesDirectory);
  
  const articles = fileNames
    .filter(name => name.endsWith('.md'))
    .map(name => {
      const fullPath = path.join(articlesDirectory, name);
      const fileContents = fs.readFileSync(fullPath, 'utf8');
      const { data, content } = matter(fileContents);
      
      return {
        slug: name.replace(/\.md$/, ''),
        title: data.title,
        description: data.excerpt || content.substring(0, 200) + '...',
        date: data.date,
        category: data.category,
        content: content,
        author: data.author || 'Anmody',
        tags: data.tags || []
      };
    })
    .sort((a, b) => new Date(b.date) - new Date(a.date))
    .slice(0, 20); // 最新20篇文章

  function slugify(str) {
    return str
      .normalize('NFKD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/\s+/g, '-')
      .replace(/[_\u00b7\u2022\u2014\u2013\u2014\u2013]/g, '-')
      .replace(/[^\w\u4e00-\u9fa5-]/g, '')
      .replace(/[\u4e00-\u9fa5]+/g, m => Array.from(m).map(c => chineseToPinyin(c)).join('-'))
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .toLowerCase();
  }

  function chineseToPinyin(char) {
    const pinyinMap = {
      '\u5feb\u901f\u5165\u9580': 'quick-start',
      '\u4f7f\u7528\u6307\u5357': 'user-guide',
      '\u5b89\u88fd\u6307\u5357': 'installation-guide',
      '\u5c0d\u8a71\u7ba1\u7406': 'conversation-management',
      '\u7ad9\u52d9': 'site-management',
      '\u958b\u767c\u8005\u6a21\u5f0f': 'developer-mode',
      '\u95dc\u65bc': 'about',
      'Chrome \u64f4\u5c55': 'chrome-extension',
      'ZIP \u5b89\u88fd': 'zip-installation'
    };
    return pinyinMap[char] || 'zh';
  }

  const rssItems = articles.map(article => `
    <item>
      <title><![CDATA[${article.title}]]></title>
      <description><![CDATA[${article.description}]]></description>
      <link>https://learnmarts.com/articles/${article.slug}</link>
      <guid>https://learnmarts.com/articles/${article.slug}</guid>
      <pubDate>${new Date(article.date).toUTCString()}</pubDate>
      <category><![CDATA[${slugify(article.category)}]]></category>
      ${article.tags.map(tag => `<category><![CDATA[${slugify(tag)}]]></category>`).join('\n')}
      ${article.tags.map(tag => slugify(tag)).join(', ')}
      <author><EMAIL> (${article.author})</author>
    </item>`).join('');

  const rssContent = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:content="http://purl.org/rss/1.0/modules/content/" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Anmody AI 教學部落</title>
    <description>專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學文章，助您掌握最新 AI 應用技術與開發技巧。</description>
    <link>https://learnmarts.com</link>
    <atom:link href="https://learnmarts.com/rss.xml" rel="self" type="application/rss+xml"/>
    <language>zh-Hant-HK</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <managingEditor><EMAIL> (Anmody)</managingEditor>
    <webMaster><EMAIL> (Anmody)</webMaster>
    <category>Technology</category>
    <category>AI</category>
    <category>Education</category>
    <ttl>60</ttl>
    <image>
      <url>https://learnmarts.com/favicon.ico</url>
      <title>Anmody AI 教學部落</title>
      <link>https://learnmarts.com</link>
    </image>
    ${rssItems}
  </channel>
</rss>`;

  const publicDir = path.join(process.cwd(), 'public');
  fs.writeFileSync(path.join(publicDir, 'rss.xml'), rssContent);
  console.log('✅ RSS feed generated!');
}

if (require.main === module) {
  generateRSSFeed();
}

module.exports = generateRSSFeed;
