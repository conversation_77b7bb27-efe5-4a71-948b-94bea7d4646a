#!/usr/bin/env node

// 全面路由测试脚本
// 检查所有主要路由和可能的重定向问题

const fs = require('fs');
const path = require('path');

const outDir = path.join(__dirname, '..', 'out');

// 主要路由列表
const mainRoutes = [
  'articles',
  'tags', 
  'categories',
  'announcements',
  'search',
  'about',
  'contact',
  'privacy',
  'terms',
  'archive'
];

// 动态路由列表
const dynamicRoutes = [
  'articles/[slug]',
  'tags/[tag]', 
  'categories/[category]',
  'announcements/[slug]',
  'announcements/category/[category]',  
  'announcements/tag/[tag]'
];

function testMainRoutes() {
  console.log('🔍 检查主要路由...\n');
  
  let allGood = true;
  
  for (const route of mainRoutes) {
    const routeDir = path.join(outDir, route);
    const indexHtml = path.join(routeDir, 'index.html');
    const indexTxt = path.join(routeDir, 'index.txt');
    
    console.log(`📁 /${route}/`);
    
    if (fs.existsSync(routeDir)) {
      if (fs.existsSync(indexHtml)) {
        console.log(`  ✅ index.html 存在`);
      } else {
        console.log(`  ❌ index.html 不存在`);
        allGood = false;
      }
      
      if (fs.existsSync(indexTxt)) {
        console.log(`  ⚠️  index.txt 存在 (RSC payload)`);
      } else {
        console.log(`  ℹ️  index.txt 不存在`);
      }
    } else {
      console.log(`  ❌ 路由目录不存在`);
      allGood = false;
    }
    console.log('');
  }
  
  return allGood;
}

function testDynamicRoutes() {
  console.log('🔍 检查动态路由样本...\n');
  
  // 检查一些样本动态路由
  const samples = [
    { path: 'articles/quickstart', desc: '文章页面' },
    { path: 'tags/quick-start', desc: 'Slug 化标签页面' },
    { path: 'tags/%E5%BF%AB%E9%80%9F%E5%85%A5%E9%96%80', desc: '编码标签页面' },
    { path: 'categories/%E6%95%99%E5%AD%B8', desc: '分类页面' },
    { path: 'announcements/site', desc: '公告页面' }
  ];
  
  let allGood = true;
  
  for (const sample of samples) {
    const sampleDir = path.join(outDir, sample.path);
    const indexHtml = path.join(sampleDir, 'index.html');
    
    console.log(`📄 /${sample.path}/ (${sample.desc})`);
    
    if (fs.existsSync(sampleDir) && fs.existsSync(indexHtml)) {
      console.log(`  ✅ 页面正常生成`);
    } else {
      console.log(`  ❌ 页面生成失败`);
      allGood = false;
    }
    console.log('');
  }
  
  return allGood;
}

function testRedirectRules() {
  console.log('🔍 检查重定向规则...\n');
  
  const redirectsPath = path.join(outDir, '_redirects');
  
  if (!fs.existsSync(redirectsPath)) {
    console.log('❌ _redirects 文件不存在');
    return false;
  }
  
  const redirectsContent = fs.readFileSync(redirectsPath, 'utf8');
  const lines = redirectsContent.split('\n').filter(line => 
    line.trim() && !line.startsWith('#')
  );
  
  console.log(`📋 发现 ${lines.length} 条重定向规则`);
  
  // 检查危险的通用规则
  const dangerousRules = lines.filter(line => 
    line.includes('/:') && (
      line.includes('/tags/:') ||
      line.includes('/categories/:') ||
      line.includes('/articles/:') ||
      line.includes('/announcements/:')
    )
  );
  
  if (dangerousRules.length > 0) {
    console.log('⚠️  发现可能有问题的通用规则:');
    dangerousRules.forEach(rule => {
      console.log(`  - ${rule}`);
    });
    console.log('这些规则可能会影响主要路由页面的访问');
  } else {
    console.log('✅ 没有发现危险的通用重定向规则');
  }
  
  // 检查中文标签重定向
  const chineseTagRules = lines.filter(line => 
    line.includes('/tags/') && (
      line.includes('%E') || 
      /[\u4e00-\u9fff]/.test(line)
    )
  );
  
  console.log(`\n📝 中文标签重定向规则: ${chineseTagRules.length} 条`);
  
  return dangerousRules.length === 0;
}

function generateRouteTestReport() {
  console.log('🎯 生成路由测试报告...\n');
  
  const report = {
    timestamp: new Date().toISOString(),
    mainRoutes: {},
    dynamicRoutes: {},
    redirectRules: {
      total: 0,
      dangerous: [],
      chineseTag: 0
    }
  };
  
  // 检查主要路由
  for (const route of mainRoutes) {
    const routeDir = path.join(outDir, route);
    const indexHtml = path.join(routeDir, 'index.html');
    const indexTxt = path.join(routeDir, 'index.txt');
    
    report.mainRoutes[route] = {
      exists: fs.existsSync(routeDir),
      hasIndexHtml: fs.existsSync(indexHtml),
      hasIndexTxt: fs.existsSync(indexTxt)
    };
  }
  
  // 检查重定向规则
  const redirectsPath = path.join(outDir, '_redirects');
  if (fs.existsSync(redirectsPath)) {
    const redirectsContent = fs.readFileSync(redirectsPath, 'utf8');
    const lines = redirectsContent.split('\n').filter(line => 
      line.trim() && !line.startsWith('#')
    );
    
    report.redirectRules.total = lines.length;
    report.redirectRules.dangerous = lines.filter(line => 
      line.includes('/:') && (
        line.includes('/tags/:') ||
        line.includes('/categories/:') ||
        line.includes('/articles/:') ||
        line.includes('/announcements/:')
      )
    );
    report.redirectRules.chineseTag = lines.filter(line => 
      line.includes('/tags/') && (
        line.includes('%E') || 
        /[\u4e00-\u9fff]/.test(line)
      )
    ).length;
  }
  
  // 保存报告
  const reportPath = path.join(__dirname, '..', 'route-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`📊 测试报告已保存到: route-test-report.json`);
  
  return report;
}

function main() {
  console.log('🚀 开始全面路由测试...\n');
  
  const mainRoutesOk = testMainRoutes();
  const dynamicRoutesOk = testDynamicRoutes();
  const redirectRulesOk = testRedirectRules();
  
  console.log('\n📊 测试汇总:');
  console.log(`主要路由: ${mainRoutesOk ? '✅ 通过' : '❌ 失败'}`);
  console.log(`动态路由: ${dynamicRoutesOk ? '✅ 通过' : '❌ 失败'}`);
  console.log(`重定向规则: ${redirectRulesOk ? '✅ 通过' : '❌ 失败'}`);
  
  const report = generateRouteTestReport();
  
  if (mainRoutesOk && dynamicRoutesOk && redirectRulesOk) {
    console.log('\n🎉 所有测试通过！');
    return true;
  } else {
    console.log('\n⚠️  存在问题，请检查上述输出');
    return false;
  }
}

// 运行测试
if (require.main === module) {
  main();
}
