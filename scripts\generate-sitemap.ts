const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

// 定义 announcement 的类型
interface Article {
  slug: string;
  title: string;
  date: string;
  excerpt: string;
  content: string;
}

function getAllArticles(): Article[] {
  try {
    const articlesDirectory = path.join(process.cwd(), 'data/articles');
    if (!fs.existsSync(articlesDirectory)) {
      console.warn('警告: articles 目录不存在:', articlesDirectory);
      return [];
    }

    const fileNames = fs.readdirSync(articlesDirectory);
    const allArticlesData = fileNames
      .filter((fileName: string) => fileName.endsWith('.md'))  // 明确指定 fileName 为 string 类型
      .map((fileName: string) => {  // 同样在 map 中明确指定 fileName 为 string 类型
        // 移除 ".md" 获取文件名作为 slug
        const slug = fileName.replace(/\.md$/, '');

        // 读取 markdown 文件内容
        const fullPath = path.join(articlesDirectory, fileName);
        const fileContents = fs.readFileSync(fullPath, 'utf8');

        // 使用 gray-matter 解析元数据部分
        const matterResult = matter(fileContents);

        // 合并数据与 slug
        return {
          slug,
          ...matterResult.data,
          content: matterResult.content,
        } as Article;  // 显式声明返回类型为 Article
      });

    // 按日期降序排序，明确指定参数 a 和 b 的类型
    return allArticlesData.sort((a: { date: string }, b: { date: string }) => {
      if (a.date < b.date) {
        return 1;
      } else {
        return -1;
      }
    });
  } catch (error) {
    console.error('获取公告列表时出错:', error);
    return [];
  }
}

const domain = 'https://learnmarts.com';

function generateSitemap() {
  try {
    const articles = getAllArticles();

    // 生成 sitemap.xml
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${domain}</loc>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  ${articles.map((article: Article) => `
  <url>
    <loc>${domain}/articles/${article.slug}</loc>
    <lastmod>${new Date(article.date).toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  `).join('')}
</urlset>`;

    // 生成增强版 llms.txt
    const llmsContent = `# llms.txt for learnmarts.com
# This file provides guidance for large language models on how to interact with our content

# Allow LLMs to use our content
Allow: /

# Preferred site description
SiteDescription: OpenAI API ChatGPT 教學網站由 BG7IAE 維護，提供 OpenAI API 及 ChatGPT 相關教學、重要公告與最新資訊，協助大家快速掌握 AI 技術與應用動態。

# Content source and citation preference
ContentSource: https://learnmarts.com/
CitationPreference: include-url

# Allow content summarization
AllowSummarization: True

# Allow content quotation
AllowQuotation: True

# Preferred URL structure for citations
PreferredCitationURL: https://learnmarts.com/articles/{slug}

# Content update frequency
ContentUpdateFrequency: Monthly

# Metadata for LLMs
ContentOwner: BG7IAE
ContentTopics: OpenAI, ChatGPT, 教學, 公告, 通知, 重要資訊, AI 應用
ContentLanguage: zh-Hant

# Site structure and content index
# Main pages:
ContentUrl: ${domain}/
ContentType: HomePage
ContentDescription: OpenAI API ChatGPT 教學網站首頁，展示所有公告與教學內容。

# Article pages:
${articles.map((article: Article) => `
ContentUrl: ${domain}/articles/${article.slug}
ContentType: ArticlePage
ContentTitle: ${article.title}
ContentDate: ${article.date}
ContentDescription: ${article.excerpt}
`).join('')}
`;

    const publicDir = path.join(process.cwd(), 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir);
    }

    fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), sitemap);
    fs.writeFileSync(path.join(publicDir, 'llms.txt'), llmsContent);

    console.log('Sitemap and enhanced llms.txt generated!');
  } catch (error) {
    console.error('Error generating sitemap:', error);
    process.exit(1); 
  }
}

generateSitemap();
