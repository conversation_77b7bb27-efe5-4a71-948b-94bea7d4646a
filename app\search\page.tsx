'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import ArticleCard from '@/components/ArticleCard'

interface SearchResult {
  slug: string
  title: string
  excerpt: string
  date: string | Date
  category?: string
  tags?: string[]
  article_id?: number
}

function SearchContent() {
  const searchParams = useSearchParams()
  const [query, setQuery] = useState(searchParams?.get('q') || '')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [allArticles, setAllArticles] = useState<any[]>([])

  // Load all articles on component mount
  useEffect(() => {
    const loadArticles = async () => {
      try {
        const response = await fetch('/articles-data.json')
        if (!response.ok) {
          throw new Error('Failed to load articles')
        }
        const articles = await response.json()
        setAllArticles(articles)
      } catch (err) {
        console.error('Failed to load articles:', err)
        setError('文章加载失败')
      }
    }
    loadArticles()
  }, [])

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setSearchResults([])
      setError(null)
      return
    }

    setIsLoading(true)
    setError(null)
    
    try {
      // Client-side search implementation
      const searchTerm = searchQuery.toLowerCase()
      const searchResults = allArticles.filter(article => {
        const titleMatch = article.title.toLowerCase().includes(searchTerm)
        const excerptMatch = article.excerpt?.toLowerCase().includes(searchTerm)
        const contentMatch = article.content?.toLowerCase().includes(searchTerm)
        const categoryMatch = article.category?.toLowerCase().includes(searchTerm)
        const tagsMatch = article.tags?.some((tag: string) => 
          tag.toLowerCase().includes(searchTerm)
        )
        
        return titleMatch || excerptMatch || contentMatch || categoryMatch || tagsMatch
      })

      // Sort results by relevance (title matches first)
      const sortedResults = searchResults.sort((a, b) => {
        const aTitle = a.title.toLowerCase().includes(searchTerm)
        const bTitle = b.title.toLowerCase().includes(searchTerm)
        if (aTitle && !bTitle) return -1
        if (!aTitle && bTitle) return 1
        return 0
      })

      // Convert to SearchResult format
      const results: SearchResult[] = sortedResults.map(article => ({
        slug: article.slug,
        title: article.title,
        excerpt: article.excerpt || '',
        date: article.date || article.publishedAt,
        category: article.category,
        tags: article.tags,
        article_id: article.id
      }))

      setSearchResults(results)
    } catch (error) {
      console.error('搜索出错:', error)
      setError('搜索时发生错误，请重试')
      setSearchResults([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const initialQuery = searchParams?.get('q') || ''
    if (initialQuery && allArticles.length > 0) {
      setQuery(initialQuery)
      performSearch(initialQuery)
    }
  }, [searchParams, allArticles])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    performSearch(query)
    
    // 更新URL参数
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href)
      if (query.trim()) {
        url.searchParams.set('q', query)
      } else {
        url.searchParams.delete('q')
      }
      window.history.pushState({}, '', url.toString())
    }
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section - 与首页保持一致的样式 */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-12 mb-8 rounded-lg">
        <div className="text-center">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">搜索文章</h1>
          <p className="text-xl text-blue-100 mb-6">
            在我们的知识库中查找您需要的内容
          </p>
        </div>
      </section>

      <div className="max-w-4xl mx-auto">
        {/* 搜索表单 */}
        <div className="mb-8">
          <form onSubmit={handleSearch} className="flex gap-2">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="请输入搜索关键词..."
              className="flex-1 px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 shadow-sm"
            />
            <button
              type="submit"
              disabled={isLoading}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors flex items-center gap-2 shadow-sm"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  搜索中...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  搜索
                </>
              )}
            </button>
          </form>
        </div>

        {/* 搜索结果统计 */}
        {query && (
          <div className="mb-6">
            <p className="text-gray-600">
              搜索 "<span className="font-semibold text-gray-900">{query}</span>" 的结果：
              <span className="font-semibold text-blue-600 ml-1">{searchResults.length} 篇文章</span>
            </p>
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* 结果列表 */}
        {searchResults.length > 0 ? (
          <div className="space-y-6">
            {searchResults.map((result, index) => (
              <div
                key={result.slug}
                className={`transform hover:-translate-y-1 transition-all duration-300 article-item delay-${index}`}
              >
                <ArticleCard
                  slug={result.slug}
                  title={result.title}
                  excerpt={result.excerpt}
                  date={result.date}
                  category={result.category}
                  tags={result.tags}
                />
              </div>
            ))}
          </div>
        ) : query && !isLoading && !error ? (
          <div className="text-center py-16">
            <div className="mb-6">
              <svg className="w-20 h-20 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">未找到相关文章</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              很抱歉，没有找到与 "<span className="font-semibold">{query}</span>" 相关的文章。尝试使用其他关键词，或者浏览我们的分类和标签。
            </p>
            <div className="flex justify-center gap-4 flex-wrap">
              <Link 
                href="/categories" 
                className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                浏览分类
              </Link>
              <Link 
                href="/tags" 
                className="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors"
              >
                浏览标签
              </Link>
              <Link 
                href="/articles" 
                className="inline-block bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
              >
                所有文章
              </Link>
            </div>
          </div>
        ) : !query ? (
          <div className="text-center py-16">
            <div className="mb-6">
              <svg className="w-20 h-20 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">开始搜索</h3>
            <p className="text-gray-600 mb-8">
              输入关键词来搜索文章、教程和公告
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-lg mx-auto">
              <h4 className="font-semibold text-gray-900 mb-2">搜索建议</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 尝试使用具体的技术关键词，如 "ChatGPT"、"OpenAI"</li>
                <li>• 使用文章分类名称，如 "教學"、"指南"</li>
                <li>• 搜索标签内容，如 "API"、"開發"</li>
              </ul>
            </div>
          </div>
        ) : null}

        {/* 快速导航 */}
        {searchResults.length > 0 && (
          <div className="mt-12 text-center">
            <Link 
              href="/articles" 
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              查看所有文章
            </Link>
          </div>
        )}

        {/* 返回首页按钮 */}
        <div className="flex justify-center mt-8">
          <Link 
            href="/" 
            className="inline-flex items-center gap-2 text-gray-600 hover:text-blue-600 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回首页
          </Link>
        </div>
      </div>
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">加载搜索页面...</p>
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  )
}
