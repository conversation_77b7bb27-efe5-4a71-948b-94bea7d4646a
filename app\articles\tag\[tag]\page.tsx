import { getAllArticles } from '@/lib/articles'
import Link from 'next/link'
import { notFound } from 'next/navigation'

export async function generateStaticParams() {
  const { getAllArticles } = await import('@/lib/articles');
  const all = getAllArticles();
  const tags = Array.from(new Set(
    all.flatMap(item => Array.isArray(item.tags) ? item.tags : [])
  ));
  return tags.map(tag => ({ tag: encodeURIComponent(tag) }));
}

export default function TagPage({ params }: { params: { tag: string } }) {
  const { tag } = params
  const all = getAllArticles()
  // 兼容：对比时对 tag 做 decodeURIComponent 和 trim
  const normalizedTag = decodeURIComponent(tag).trim()
  const list = all.filter(item => Array.isArray(item.tags) && item.tags.some(t => t.trim() === normalizedTag))
  if (list.length === 0) {
    if (typeof window !== 'undefined') {
      setTimeout(() => { window.location.href = '/'; }, 5000);
    }
    return (
      <div className="p-8 text-gray-400 flex flex-col items-center justify-center">
        <div className="mb-4">搵唔到相關文章。5秒後自動返主頁。</div>
        <a href="/" className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">返主頁</a>
      </div>
    );
  }
  return (
    <div className="max-w-2xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">標籤：{tag}</h1>
      <ul className="space-y-4">
        {list.map(a => (
          <li key={a.slug}>
            <Link href={`/articles/${a.slug}`} className="list-card relative block p-6 mb-2 transition-all duration-300 hover:border-blue-500/50">
  <div>
    <div className="font-semibold text-lg mb-1 text-white">{a.title}</div>
    <div className="text-gray-300 text-sm mt-1 line-clamp-3">{a.excerpt}</div>
  </div>
</Link>
          </li>
        ))}
      </ul>
      <div className="flex justify-center mt-8">
        <a href="/" className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">返主頁</a>
      </div>
    </div>
  )
}
