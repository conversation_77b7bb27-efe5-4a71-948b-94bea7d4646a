import { getAllArticles } from '@/lib/articles'
import { slugifyCategory } from '@/lib/tags'
import Link from 'next/link'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '文章分類 | LearnMarts',
  description: '瀏覽所有文章分類，快速找到您感興趣的 AI 技術教學內容。',
  openGraph: {
    title: '文章分類 | LearnMarts',
    description: '瀏覽所有文章分類，快速找到您感興趣的 AI 技術教學內容。',
    url: 'https://learnmarts.com/categories',
    type: 'website',
  },
  alternates: {
    canonical: 'https://learnmarts.com/categories',
  },
}

export default function CategoriesPage() {
  const articles = getAllArticles()
  
  // 統計每個分類的文章數量
  const categoryStats = articles.reduce((acc, article) => {
    if (article.category) {
      acc[article.category] = (acc[article.category] || 0) + 1
    }
    return acc
  }, {} as Record<string, number>)

  const categories = Object.entries(categoryStats).sort(([,a], [,b]) => b - a)

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">文章分類</h1>
        <p className="text-xl text-gray-600">
          按分類瀏覽我們的 AI 技術教學文章
        </p>
      </div>

      {categories.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map(([category, count]) => (
            <Link
              key={category}
              href={`/categories/${slugifyCategory(category)}`}
              className="block bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-300 group"
            >
              <h2 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600">
                {category}
              </h2>
              <p className="text-gray-600 mb-4">
                {count} 篇文章
              </p>
              <div className="flex items-center text-blue-600 group-hover:text-blue-700">
                <span className="text-sm font-medium">查看文章</span>
                <svg className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">暫無分類</p>
        </div>
      )}
    </div>
  )
}
