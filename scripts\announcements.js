const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

function getAllAnnouncements() {
  try {
    const announcementsDirectory = path.join(process.cwd(), 'data/announcements');
    if (!fs.existsSync(announcementsDirectory)) {
      console.warn('警告: announcements 目錄不存在:', announcementsDirectory);
      return [];
    }

    const fileNames = fs.readdirSync(announcementsDirectory);
    const allAnnouncementsData = fileNames
      .filter(fileName => fileName.endsWith('.md'))
      .map(fileName => {
        // 移除 ".md" 获取文件名作为 slug
        const slug = fileName.replace(/\.md$/, '');

        // 读取 markdown 文件内容
        const fullPath = path.join(announcementsDirectory, fileName);
        const fileContents = fs.readFileSync(fullPath, 'utf8');

        // 使用 gray-matter 解析元数据部分
        const matterResult = matter(fileContents);

        // 合并数据与 slug
        return {
          slug,
          ...matterResult.data,
          content: matterResult.content
        };
      });

    // 按日期降序排序
    return allAnnouncementsData.sort((a, b) => {
      if (a.date < b.date) {
        return 1;
      } else {
        return -1;
      }
    });
  } catch (error) {
    console.error('取得公告列表時發生錯誤:', error);
    return [];
  }
}

module.exports = {
  getAllAnnouncements
};
