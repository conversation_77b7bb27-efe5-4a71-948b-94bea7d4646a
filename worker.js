export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    try {
      // First, handle the special redirect logic for Chinese tags/categories
      const match = url.pathname.match(/^\/(categories|tags)\/([^/]+)(\/)?$/);
      if (match) {
        const [full, type, raw, slash] = match;
        // Check if the tag/category contains Chinese characters and needs encoding
        if (/[\u4e00-\u9fa5]/.test(raw)) {
          const encoded = encodeURIComponent(raw);
          // Redirect to the URL-encoded version
          return Response.redirect(
            `${url.origin}/${type}/${encoded}${slash || ""}`,
            301
          );
        }
      }

      // For all other requests, attempt to serve a static asset from KV
      // The [site] config in wrangler.toml provides env.ASSETS
      return await env.ASSETS.fetch(request);
    } catch (e) {
      // If the asset is not found, env.ASSETS.fetch() will throw an exception.
      // We can try to return a custom 404 page.
      if (e.constructor.name === "NotFoundError") {
        try {
          const notFoundResponse = await env.ASSETS.fetch(
            new Request(`${url.origin}/404.html`, request)
          );
          return new Response(notFoundResponse.body, {
            ...notFoundResponse,
            status: 404,
            statusText: "Not Found",
          });
        } catch (err) {
          // If 404.html doesn't exist either, return a plain text response.
          return new Response("Not Found", { status: 404 });
        }
      }

      // For other errors, return a generic 500 error page.
      return new Response(`An error occurred: ${e.message}`, { status: 500 });
    }
  },
};
