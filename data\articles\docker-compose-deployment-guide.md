---
title: Docker Compose 現代化部署完整指南
date: 2025-07-01T00:00:00.000Z
excerpt: 深入學習 Docker Compose V2 的現代化容器編排技術，包含完整的部署示例、最佳實踐和故障排除指南。
category: 教學
tags:
  - Docker
  - Docker Compose
  - 容器化
  - DevOps
  - 部署
article_id: 7
author: Anmody
readTime: 8 分鐘閱讀
tags_slug:
  - docker
  - docker-compose
  - containerization
  - devops
  - deployment
category_slug: tutorials
---

# Docker Compose 現代化部署完整指南

在現代軟體開發中，容器化技術已成為不可或缺的一環。Docker Compose 作為 Docker 官方的容器編排工具，讓我們能夠輕鬆管理多容器應用程式。本文將深入介紹 Docker Compose V2 的使用方法，並提供實用的部署示例。

## 🚨 重要提醒：使用現代版本的 Docker Compose

**請注意**：本教程使用的是現代版本的 Docker Compose（V2），命令格式為 `docker compose`（中間有空格），而非舊版本的 `docker-compose`（中間有連字符）。

### 版本差異對比

| 項目 | 舊版本 (V1) | 新版本 (V2) |
|------|-------------|-------------|
| 命令格式 | `docker-compose` | `docker compose` |
| 安裝方式 | 獨立安裝 | Docker Desktop 內建 |
| 性能 | 較慢 | 顯著提升 |
| 功能 | 基礎功能 | 增強功能 + 新特性 |

## 📋 前置需求

在開始之前，請確保您的系統已安裝：

1. **Docker Desktop** (推薦) 或 **Docker Engine** (Linux)
2. **Docker Compose V2** (Docker Desktop 內建)

### 檢查安裝版本

```bash
# 檢查 Docker 版本
docker --version

# 檢查 Docker Compose 版本 (V2)
docker compose version

# 如果顯示舊版本，請升級 Docker Desktop
```

## 🏗️ Docker Compose 基礎概念

### 什麼是 Docker Compose？

Docker Compose 是一個用於定義和運行多容器 Docker 應用程式的工具。通過一個 YAML 文件來配置應用程式的服務，然後使用單一命令來創建並啟動所有服務。

### 核心組件

- **Services（服務）**：應用程式的不同組件（如 Web 服務、資料庫等）
- **Networks（網路）**：容器間的通信網路
- **Volumes（卷）**：數據持久化存儲

## 📝 基礎 docker-compose.yml 結構

```yaml
version: '3.8'  # Compose 文件版本

services:
  # 服務定義
  web:
    image: nginx:alpine
    ports:
      - "80:80"
    
  database:
    image: postgres:13
    environment:
      POSTGRES_PASSWORD: secret

networks:
  # 網路定義
  default:
    driver: bridge

volumes:
  # 卷定義
  db_data:
```

## 🚀 實戰示例：完整的 Web 應用部署

讓我們創建一個包含 Web 應用、資料庫和快取的完整示例：

### 1. 項目結構

```
my-app/
├── docker-compose.yml
├── .env
├── nginx/
│   └── nginx.conf
├── app/
│   ├── Dockerfile
│   └── src/
└── data/
    └── postgres/
```

### 2. 完整的 docker-compose.yml

```yaml
version: '3.8'

services:
  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: my-app-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - frontend
    restart: unless-stopped

  # Node.js 應用
  app:
    build:
      context: ./app
      dockerfile: Dockerfile
    container_name: my-app-backend
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - frontend
      - backend
    restart: unless-stopped
    volumes:
      - ./app/uploads:/app/uploads

  # PostgreSQL 資料庫
  postgres:
    image: postgres:15-alpine
    container_name: my-app-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./data/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis 快取
  redis:
    image: redis:7-alpine
    container_name: my-app-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 監控工具 (可選)
  adminer:
    image: adminer:latest
    container_name: my-app-adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - backend
    restart: unless-stopped
    profiles:
      - debug

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
```

### 3. 環境變數文件 (.env)

```bash
# 資料庫配置
POSTGRES_DB=myapp
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here

# Redis 配置
REDIS_PASSWORD=your_redis_password_here

# 應用配置
NODE_ENV=production
APP_PORT=3000

# SSL 配置 (如果使用 HTTPS)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
```

## 🔧 常用 Docker Compose 命令

### 基本操作

```bash
# 啟動所有服務 (後台運行)
docker compose up -d

# 查看服務狀態
docker compose ps

# 查看服務日誌
docker compose logs

# 查看特定服務日誌
docker compose logs app

# 停止所有服務
docker compose down

# 停止並刪除卷
docker compose down -v

# 重新構建並啟動
docker compose up --build -d
```

### 進階操作

```bash
# 擴展服務實例
docker compose up --scale app=3 -d

# 執行一次性命令
docker compose exec app npm run migrate

# 進入容器 shell
docker compose exec app sh

# 查看資源使用情況
docker compose top

# 驗證配置文件
docker compose config
```

## 🛡️ 安全最佳實踐

### 1. 環境變數管理

```yaml
# 使用 .env 文件
environment:
  - DATABASE_URL=${DATABASE_URL}
  - API_KEY=${API_KEY}

# 使用 Docker secrets (Swarm 模式)
secrets:
  - db_password
  - api_key
```

### 2. 網路隔離

```yaml
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true  # 內部網路，無法訪問外網
```

### 3. 資源限制

```yaml
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
```

## 🔍 故障排除指南

### 常見問題及解決方案

#### 1. 服務無法啟動

```bash
# 檢查服務狀態
docker compose ps

# 查看詳細日誌
docker compose logs --tail=50 service_name

# 檢查配置文件語法
docker compose config
```

#### 2. 網路連接問題

```bash
# 檢查網路配置
docker network ls
docker network inspect my-app_default

# 測試容器間連接
docker compose exec app ping postgres
```

#### 3. 卷掛載問題

```bash
# 檢查卷狀態
docker volume ls
docker volume inspect my-app_postgres_data

# 檢查文件權限
docker compose exec app ls -la /app/uploads
```

### 調試技巧

```bash
# 以調試模式啟動
docker compose --profile debug up -d

# 查看容器內部
docker compose exec app sh

# 監控資源使用
docker stats $(docker compose ps -q)
```

## 📊 監控和日誌管理

### 1. 日誌配置

```yaml
services:
  app:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 2. 健康檢查

```yaml
services:
  app:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 🚀 生產環境部署建議

### 1. 使用多階段構建

```dockerfile
# Dockerfile 示例
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 2. 配置文件分離

```bash
# 開發環境
docker compose -f docker-compose.yml -f docker-compose.dev.yml up

# 生產環境
docker compose -f docker-compose.yml -f docker-compose.prod.yml up
```

### 3. 自動化部署腳本

```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 開始部署..."

# 拉取最新代碼
git pull origin main

# 構建並啟動服務
docker compose build --no-cache
docker compose up -d

# 等待服務就緒
echo "⏳ 等待服務啟動..."
sleep 30

# 健康檢查
if docker compose exec app curl -f http://localhost:3000/health; then
    echo "✅ 部署成功！"
else
    echo "❌ 部署失敗，正在回滾..."
    docker compose down
    exit 1
fi

# 清理舊映像
docker image prune -f

echo "🎉 部署完成！"
```

## 📚 總結

Docker Compose V2 為現代應用部署提供了強大而靈活的解決方案。通過本教程，您學會了：

- ✅ Docker Compose V2 的基本概念和命令
- ✅ 編寫完整的 docker-compose.yml 配置
- ✅ 實施安全最佳實踐
- ✅ 故障排除和調試技巧
- ✅ 生產環境部署策略

記住使用現代版本的 `docker compose` 命令，並根據您的具體需求調整配置。隨著經驗的積累，您將能夠構建更複雜和高效的容器化應用。

## 🔗 相關資源

- [Docker Compose 官方文檔](https://docs.docker.com/compose/)
- [Docker Hub](https://hub.docker.com/)
- [Compose 文件參考](https://docs.docker.com/compose/compose-file/)

---

*如果您在部署過程中遇到問題，歡迎在評論區分享，我們會盡快為您解答！*
