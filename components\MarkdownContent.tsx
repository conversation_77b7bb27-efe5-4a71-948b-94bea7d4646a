'use client'
import React, { useState, useRef } from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeHighlight from 'rehype-highlight'
import 'highlight.js/styles/github-dark.css'

// 遞迴提取 React children 的純文字內容
function extractTextFromChildren(children: React.ReactNode): string {
  if (typeof children === 'string' || typeof children === 'number') {
    return String(children);
  }
  if (Array.isArray(children)) {
    return children.map(extractTextFromChildren).join('');
  }
  if (React.isValidElement(children)) {
    // cast to ReactElement with correct props type so props.children is ReactNode
    return extractTextFromChildren((children as React.ReactElement<{ children: React.ReactNode }>).props.children);
  }
  return '';
}

// 複製按鈕組件
function CopyButton({ code }: { code: string }) {
  const [copied, setCopied] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleCopy = () => {
    navigator.clipboard.writeText(code).then(() => {
      setCopied(true)
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
      timeoutRef.current = setTimeout(() => setCopied(false), 2000)
    })
  }

  return (
    <button
      className={`code-copy-button${copied ? ' copied' : ''}`}
      onClick={handleCopy}
      type="button"
    >
      {copied ? '已複製!' : '複製'}
    </button>
  )
}

export default function MarkdownContent({ content }: { content: string }) {
  const components = {
    p({ children }: { children: React.ReactNode[] }) {
      if (
        React.Children.toArray(children).some(
          (child) =>
            React.isValidElement(child) &&
            (child.type === 'pre' || (child as any).props?.node?.tagName === 'pre')
        )
      ) {
        return <>{children}</>
      }
      return <p>{children}</p>
    },
    pre({ children, ...props }: any) {
      // children[0] 應該是 <code>
      const codeElement = React.Children.toArray(children)[0];
      // 用遞迴函數正確提取純文字內容，並明確類型
      let codeText = '';
      if (React.isValidElement(codeElement)) {
        // cast to ReactElement with correct props type so props.children is ReactNode
        codeText = extractTextFromChildren((codeElement as React.ReactElement<{ children: React.ReactNode }>).props.children);
      }
      return (
        <pre {...props}>
          <CopyButton code={codeText} />
          {children}
        </pre>
      );
    },
    code({ inline, className, children, ...props }: any) {
      if (inline) {
        return (
          <code className="inline-code" {...props}>
            {children}
          </code>
        );
      }
      return <code className={className || ''} {...props}>{children}</code>;
    }
  }

  return (
    <div suppressHydrationWarning className="prose prose-slate max-w-none animate-fade-in">
      <ReactMarkdown rehypePlugins={[rehypeHighlight]} components={components as any}>
        {content}
      </ReactMarkdown>
    </div>
  )
}