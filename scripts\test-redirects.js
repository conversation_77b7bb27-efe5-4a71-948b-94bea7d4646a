#!/usr/bin/env node

// 测试重定向规则
const fs = require('fs');
const path = require('path');

function testRedirectRules() {
  console.log('🔍 检查重定向规则...\n');
  
  const redirectsPath = path.join(__dirname, '..', 'out', '_redirects');
  
  if (!fs.existsSync(redirectsPath)) {
    console.error('❌ _redirects 文件不存在');
    return false;
  }
  
  const redirectsContent = fs.readFileSync(redirectsPath, 'utf8');
  const lines = redirectsContent.split('\n').filter(line => 
    line.trim() && !line.startsWith('#')
  );
  
  console.log('📋 发现重定向规则:');
  lines.forEach((line, index) => {
    console.log(`  ${index + 1}. ${line}`);
  });
  
  // 检查问题性规则
  const problematicRules = lines.filter(line => 
    line.includes('/articles/:slug') || 
    line.includes('/articles/ ')
  );
  
  console.log('\n🔍 检查问题性规则:');
  if (problematicRules.length === 0) {
    console.log('  ✅ 没有发现会影响 /articles/ 页面的规则');
  } else {
    console.log('  ❌ 发现可能有问题的规则:');
    problematicRules.forEach(rule => {
      console.log(`    - ${rule}`);
    });
  }
  
  // 检查文章目录结构
  console.log('\n📁 检查文章目录结构:');
  const articlesDir = path.join(__dirname, '..', 'out', 'articles');
  if (fs.existsSync(articlesDir)) {
    const files = fs.readdirSync(articlesDir);
    console.log(`  ✅ 发现 ${files.length} 个文件/目录:`);
    files.forEach(file => {
      const filePath = path.join(articlesDir, file);
      const isDir = fs.statSync(filePath).isDirectory();
      console.log(`    ${isDir ? '📁' : '📄'} ${file}`);
    });
    
    // 检查 index.html 是否存在
    const indexHtml = path.join(articlesDir, 'index.html');
    if (fs.existsSync(indexHtml)) {
      console.log('  ✅ /articles/index.html 存在');
    } else {
      console.log('  ❌ /articles/index.html 不存在');
    }
  } else {
    console.log('  ❌ articles 目录不存在');
  }
  
  return true;
}

// 运行测试
if (require.main === module) {
  testRedirectRules();
}
