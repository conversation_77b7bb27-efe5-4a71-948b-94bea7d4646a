import { getAllArticles } from '@/lib/articles'
import { slugifyTag } from '@/lib/tags'
import Link from 'next/link'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '文章標籤 | LearnMarts',
  description: '瀏覽所有文章標籤，快速找到您感興趣的 AI 技術教學內容。',
  openGraph: {
    title: '文章標籤 | LearnMarts',
    description: '瀏覽所有文章標籤，快速找到您感興趣的 AI 技術教學內容。',
    url: 'https://learnmarts.com/tags',
    type: 'website',
  },
  alternates: {
    canonical: 'https://learnmarts.com/tags',
  },
}

export default function TagsPage() {
  const articles = getAllArticles()
  
  // 統計每個標籤的文章數量
  const allTags = articles.flatMap(article => article.tags || [])
  const tagStats = allTags.reduce((acc, tag) => {
    acc[tag] = (acc[tag] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const tags = Object.entries(tagStats).sort(([,a], [,b]) => b - a)

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">文章標籤</h1>
        <p className="text-xl text-gray-600">
          按標籤瀏覽我們的 AI 技術教學文章
        </p>
      </div>

      {tags.length > 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="flex flex-wrap gap-3">
            {tags.map(([tag, count]) => (
              <Link
                key={tag}
                href={`/tags/${slugifyTag(tag)}`}
                className="inline-block bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full transition-colors group"
              >
                <span className="font-medium">#{tag}</span>
                <span className="ml-2 text-sm text-gray-500 group-hover:text-blue-600">
                  ({count})
                </span>
              </Link>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">暫無標籤</p>
        </div>
      )}
    </div>
  )
}
