import ArticleCard from '@/components/ArticleCard'
import { getAllArticles } from '@/lib/articles'
import type { Metadata } from 'next'
import Script from 'next/script'
import Link from 'next/link'
import Sidebar from '@/components/Sidebar'

export const metadata: Metadata = {
  title: 'Anmody AI 教學部落 | 最新 AI 技術文章與教學',
  description: '探索最新的 AI 技術文章，包含 OpenAI API、ChatGPT 深度教學，以及實用的程式開發技巧。專業的 AI 教學部落，助您掌握人工智慧應用。',
}

export default function Home() {
  const articles = getAllArticles()
  const featuredArticles = articles.slice(0, 3)
  const recentArticles = articles.slice(3, 9)

  // 結構化資料用的 JSON-LD
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'Anmody AI 教學部落',
    description: '專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學文章，助您掌握最新 AI 應用技術與開發技巧。',
    url: 'https://learnmarts.com',
    publisher: {
      '@type': 'Organization',
      name: 'Anmody',
      url: 'https://learnmarts.com',
      logo: {
        '@type': 'ImageObject',
        url: 'https://learnmarts.com/images/logo.png'
      }
    },
    blogPost: articles.map((article, index) => ({
      '@type': 'BlogPosting',
      headline: article.title,
      datePublished: article.date,
      description: article.excerpt,
      author: {
        '@type': 'Organization',
        name: 'Anmody'
      },
      publisher: {
        '@type': 'Organization',
        name: 'Anmody'
      },
      url: `https://learnmarts.com/articles/${article.slug}`
    })),
    inLanguage: 'zh-Hant-HK'
  }

  return (
    <>
      <Script
        id="structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16 mb-12 rounded-lg">
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Anmody AI 教學部落
          </h1>
          <p className="text-xl md:text-2xl mb-6 text-blue-100">
            探索 AI 技術的無限可能
          </p>
          <p className="text-lg mb-8 text-blue-50 max-w-2xl mx-auto">
            專業的 OpenAI API 與 ChatGPT 教學，助您掌握最新的人工智慧應用技術
          </p>
          <Link
            href="/articles"
            className="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
          >
            開始學習
          </Link>
        </div>
      </section>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Featured Articles */}
          {featuredArticles.length > 0 && (
            <section className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="w-1 h-6 bg-blue-600 mr-3"></span>
                精選文章
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredArticles.map((article, index) => (
                  <div
                    key={article.slug}
                    className={`transform hover:-translate-y-1 transition-all duration-300 article-item delay-${index}`}
                  >
                    <ArticleCard {...article} featured />
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Recent Articles */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <span className="w-1 h-6 bg-green-600 mr-3"></span>
              最新文章
            </h2>
            <div className="space-y-6">
              {recentArticles.map((article, index) => (
                <div
                  key={article.slug}
                  className={`transform hover:-translate-y-1 transition-all duration-300 article-item delay-${index + 3}`}
                >
                  <ArticleCard {...article} />
                </div>
              ))}
            </div>

            {articles.length > 9 && (
              <div className="text-center mt-8">
                <Link
                  href="/articles"
                  className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  查看更多文章
                </Link>
              </div>
            )}
          </section>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Sidebar articles={articles} />
        </div>
      </div>
    </>
  )
}