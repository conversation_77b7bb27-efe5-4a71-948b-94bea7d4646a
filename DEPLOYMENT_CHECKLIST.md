# 部署检查清单

## 部署前检查 ✅

### 1. 构建验证
- [x] `npm run build` 成功完成
- [x] `out` 目录生成完整
- [x] 标签目录结构正确（既有 slug 版本又有编码版本）
- [x] 静态文件完整（包括 `_redirects`, `tag-redirect.js`）
- [x] 修复了 `/articles/` 页面重定向问题

### 2. 重定向规则验证
- [x] 移除了会影响 `/articles/` 页面的通用重定向规则
- [x] 保留了中文标签的重定向规则
- [x] 确保 `/articles/index.html` 正常访问
- [x] `_redirects` 文件语法正确

### 2. 文件检查
- [x] `public/_redirects` - Cloudflare Pages 重定向规则
- [x] `public/js/tag-redirect.js` - 客户端重定向脚本
- [x] `public/sitemap.xml` - 使用 slug 化的标签 URL
- [x] `public/robots.txt` - 搜索引擎规则
- [x] `wrangler.toml` - 项目名称保持为 `teaching-anmody-com`

### 3. 标签测试
- [x] 所有中文标签都有对应的 slug 版本
- [x] slug 版本页面可正常生成
- [x] 编码版本页面存在（兼容性）
- [x] 标签映射一致性验证

### 4. URL 结构
```
推荐使用（SEO 友好）:
https://learnmarts.com/tags/quick-start      ✅
https://learnmarts.com/tags/user-guide       ✅
https://learnmarts.com/tags/chrome-extension ✅

兼容重定向:
https://learnmarts.com/tags/快速入門          → /tags/quick-start
https://learnmarts.com/tags/%E5%BF%AB%E9%80%9F%E5%85%A5%E9%96%80 → /tags/quick-start
```

## Cloudflare Pages 部署步骤

### 0. 重要配置说明 ⚠️
**请保持以下配置不变**：
```toml
name = "teaching-anmody-com"
compatibility_date = "2024-01-25" 
pages_build_output_dir = "out"
```

### 1. 连接 GitHub 仓库
- 选择 `learnmarts.com` 仓库
- 设置构建分支为 `main`

### 2. 构建设置
- 构建命令: `npm run build`
- 构建输出目录: `out`
- Node.js 版本: `18` 或更高

### 3. 环境变量（如需要）
- 暂无特殊环境变量需求

### 4. 自定义域名
- 添加域名: `learnmarts.com`
- 配置 DNS 指向 Cloudflare Pages

## 部署后验证 🔍

### 1. 基本功能测试
- [ ] 首页正常加载
- [ ] 文章页面正常显示
- [ ] 搜索功能正常

### 2. 标签页面测试
- [ ] 访问 `/tags/quick-start` 正常显示
- [ ] 访问 `/tags/快速入門` 自动重定向到 `/tags/quick-start`
- [ ] 访问编码 URL 正确重定向
- [ ] 标签列表页面链接正确

### 3. 重定向测试
- [ ] 旧域名 `teaching.anmody.com` 重定向到 `learnmarts.com`（如配置）
- [ ] 中文标签 URL 重定向正常工作
- [ ] 404 页面显示正常

### 4. SEO 验证
- [ ] `robots.txt` 可访问
- [ ] `sitemap.xml` 可访问并包含正确 URL
- [ ] RSS feed 可正常访问
- [ ] Meta 标签正确显示

### 5. 性能检查
- [ ] 页面加载速度正常
- [ ] 静态资源缓存正确
- [ ] 图片资源正常显示

## 常见问题排查

### 标签页面 404
1. 检查 `_redirects` 文件是否在构建输出中
2. 验证 Cloudflare Pages 是否支持 `_redirects`
3. 检查客户端重定向脚本是否正常加载

### 重定向不工作
1. 确认重定向规则语法正确
2. 检查 URL 编码匹配
3. 验证重定向优先级

### 搜索引擎收录
1. 提交新的 sitemap 到 Google Search Console
2. 监控索引状态
3. 检查 canonical URL 设置

## 监控建议

### 1. 错误监控
- 设置 404 错误告警
- 监控重定向链路
- 检查标签页面访问量

### 2. 性能监控
- 页面加载时间
- 核心 Web 指标
- 用户体验指标

### 3. SEO 监控
- 搜索引擎索引状态
- 关键词排名变化
- 点击率统计

---

**部署完成后，请逐一检查上述项目并更新状态！**
