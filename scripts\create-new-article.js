#!/usr/bin/env node

/**
 * 新文章创建脚本
 * 用法: node scripts/create-new-article.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 获取下一个可用的文章ID
function getNextArticleId() {
  const articlesDir = path.join(__dirname, '../data/articles');
  const files = fs.readdirSync(articlesDir).filter(file => file.endsWith('.md'));
  
  let maxId = 0;
  files.forEach(file => {
    const content = fs.readFileSync(path.join(articlesDir, file), 'utf8');
    const match = content.match(/article_id:\s*(\d+)/);
    if (match) {
      maxId = Math.max(maxId, parseInt(match[1]));
    }
  });
  
  return maxId + 1;
}

// 生成文件名slug
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格转连字符
    .replace(/-+/g, '-') // 合并多个连字符
    .replace(/^-|-$/g, ''); // 移除首尾连字符
}

// 生成标签slug
function generateTagSlug(tag) {
  const tagSlugMap = {
    'OpenAI': 'openai',
    'ChatGPT': 'chatgpt',
    'API': 'api',
    'Docker': 'docker',
    'Docker Compose': 'docker-compose',
    'Kubernetes': 'kubernetes',
    'DevOps': 'devops',
    'JavaScript': 'javascript',
    'Python': 'python',
    'Node.js': 'nodejs',
    '快速入門': 'quick-start',
    '使用指南': 'user-guide',
    '最佳實踐': 'best-practices',
    '故障排除': 'troubleshooting',
    '安全配置': 'security-config',
    '性能優化': 'performance-optimization',
    '容器化': 'containerization',
    '部署': 'deployment',
    '監控': 'monitoring',
    '自動化': 'automation'
  };
  
  return tagSlugMap[tag] || tag.toLowerCase().replace(/\s+/g, '-');
}

// 获取分类slug
function getCategorySlug(category) {
  const categorySlugMap = {
    '教學': 'tutorials',
    '用戶指南': 'user-guide-cat',
    '站務公告': 'site-announcements'
  };
  
  return categorySlugMap[category] || 'tutorials';
}

// 估算阅读时间
function estimateReadTime(content) {
  const wordsPerMinute = 200; // 中文阅读速度
  const wordCount = content.length;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return `${minutes} 分鐘閱讀`;
}

// 生成文章模板
function generateArticleTemplate(answers) {
  const nextId = getNextArticleId();
  const slug = generateSlug(answers.title);
  const tagSlugs = answers.tags.map(tag => generateTagSlug(tag));
  const categorySlug = getCategorySlug(answers.category);
  const currentDate = new Date().toISOString();
  
  return `---
title: ${answers.title}
date: ${currentDate}
excerpt: ${answers.excerpt}
category: ${answers.category}
tags:
${answers.tags.map(tag => `  - ${tag}`).join('\n')}
article_id: ${nextId}
author: ${answers.author || 'Anmody'}
readTime: 5 分鐘閱讀
tags_slug:
${tagSlugs.map(slug => `  - ${slug}`).join('\n')}
category_slug: ${categorySlug}
---

# ${answers.title}

${answers.excerpt}

## 🎯 学习目标

通过本文，您将学会：
- 学习目标1
- 学习目标2
- 学习目标3

## 📋 前置需求

在开始之前，请确保您具备：
- 基础知识要求1
- 基础知识要求2

## 🚀 正文内容

### 基础概念

在这里介绍相关的基础概念。

### 实践操作

#### 步骤1：准备工作

详细描述第一步需要做什么。

\`\`\`bash
# 提供具体的命令示例
echo "Hello World"
\`\`\`

#### 步骤2：核心配置

\`\`\`yaml
# 提供配置文件示例
version: '3.8'
services:
  example:
    image: example:latest
\`\`\`

## 💡 最佳实践

1. **实践建议1**
   - 具体说明

2. **实践建议2**
   - 具体说明

## 🔧 故障排除

### 常见问题1

**问题描述**：描述可能遇到的问题

**解决方案**：
\`\`\`bash
# 提供解决命令
solution-command --fix
\`\`\`

## 📚 总结

在本文中，我们学习了：
- 要点1
- 要点2
- 要点3

## 🔗 相关资源

- [相关链接1](https://example.com)
- [相关链接2](https://example.com)

---

*如果您在实践过程中遇到问题，欢迎在评论区分享，我们会尽快为您解答！*
`;
}

// 问答流程
async function askQuestions() {
  const answers = {};
  
  console.log('🎉 欢迎使用文章创建工具！\n');
  
  // 文章标题
  answers.title = await new Promise(resolve => {
    rl.question('📝 请输入文章标题: ', resolve);
  });
  
  // 文章摘要
  answers.excerpt = await new Promise(resolve => {
    rl.question('📄 请输入文章摘要 (1-2句话): ', resolve);
  });
  
  // 文章分类
  console.log('\n📂 可选分类:');
  console.log('1. 教學 (技术教程)');
  console.log('2. 用戶指南 (产品使用说明)');
  console.log('3. 站務公告 (网站公告)');
  
  const categoryChoice = await new Promise(resolve => {
    rl.question('请选择分类 (1-3): ', resolve);
  });
  
  const categories = ['教學', '用戶指南', '站務公告'];
  answers.category = categories[parseInt(categoryChoice) - 1] || '教學';
  
  // 文章标签
  const tagsInput = await new Promise(resolve => {
    rl.question('🏷️  请输入标签 (用逗号分隔): ', resolve);
  });
  
  answers.tags = tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag);
  
  // 作者
  answers.author = await new Promise(resolve => {
    rl.question('👤 作者姓名 (默认: Anmody): ', (input) => {
      resolve(input || 'Anmody');
    });
  });
  
  return answers;
}

// 主函数
async function main() {
  try {
    const answers = await askQuestions();
    
    // 生成文件名
    const fileName = generateSlug(answers.title) + '.md';
    const filePath = path.join(__dirname, '../data/articles', fileName);
    
    // 检查文件是否已存在
    if (fs.existsSync(filePath)) {
      console.log(`\n❌ 文件 ${fileName} 已存在！`);
      rl.close();
      return;
    }
    
    // 生成文章内容
    const content = generateArticleTemplate(answers);
    
    // 写入文件
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`\n✅ 文章创建成功！`);
    console.log(`📁 文件路径: data/articles/${fileName}`);
    console.log(`🆔 文章ID: ${getNextArticleId()}`);
    
    console.log('\n📋 下一步操作:');
    console.log('1. 编辑文章内容');
    console.log('2. 运行 npm run build 构建项目');
    console.log('3. 运行 npx serve@latest out 预览效果');
    
  } catch (error) {
    console.error('❌ 创建文章时出错:', error.message);
  } finally {
    rl.close();
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateSlug,
  generateTagSlug,
  getCategorySlug,
  getNextArticleId
};
