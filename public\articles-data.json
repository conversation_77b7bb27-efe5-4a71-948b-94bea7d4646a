[{"slug": "Common-Terms-in-Android-Engineering", "title": "Android 工程常用術語", "excerpt": "收錄 Android 工程開發中最常見的專業術語與解釋，助你快速掌握專案結構、開發流程與調試技巧。", "date": "2025-05-17T00:00:00.000Z", "category": "教學", "tags": ["Android"], "content": "\r\n# Android 工程常用術語\r\n\r\n## 1. 基礎術語\r\n- **包（Package）**：Java/Kotlin 程式碼的組織單元，用於命名空間管理。\r\n- **類（Class）**：定義物件的藍圖，包含屬性和方法。\r\n- **方法（Method）**：類中定義的具體功能或行為。\r\n\r\n## 2. 專案結構相關\r\n- **模組（Module）**：獨立建構單元，如 app 模組、函式庫模組。\r\n- **Activity**：用戶介面畫面。\r\n- **Fragment**：Activity 的子介面，模組化 UI。\r\n- **Service**：背景任務元件，無 UI。\r\n- **BroadcastReceiver**：接收廣播訊息。\r\n- **ContentProvider**：跨應用程式共享資料。\r\n- **Application**：應用程式全域進入點類別。\r\n- **Gradle**：建構工具，管理相依。\r\n- **Manifest（AndroidManifest.xml）**：應用程式設定檔。\r\n- **Resource（資源）**：版面、字串、圖片、主題等。\r\n- **AAR**：Android 函式庫檔案格式。\r\n- **APK**：應用程式安裝包。\r\n- **AAB（Android App Bundle）**：動態交付發佈格式。\r\n\r\n## 3. 程式碼與開發相關\r\n- **介面（Interface）**：方法契約。\r\n- **抽象類（Abstract Class）**：定義通用行為。\r\n- **註解（Annotation）**：如 `@Override`、`@Nullable`。\r\n- **回呼（Callback）**：非同步結果處理。\r\n- **Intent**：元件間通訊。\r\n- **Bundle**：傳遞資料的鍵值對。\r\n- **View**：UI 元素，如 Button。\r\n- **ViewModel**：管理 UI 資料，生命週期感知。\r\n- **LiveData**：可觀察資料，生命週期感知。\r\n- **DataBinding**：UI 與資料綁定。\r\n- **Room**：本地資料庫函式庫。\r\n- **Coroutine**：Kotlin 非同步框架。\r\n- **Flow**：Kotlin 響應式資料流。\r\n\r\n## 4. 架構與設計相關\r\n- **MVC/MVP/MVVM**：架構模式。\r\n- **Repository**：統一管理資料來源。\r\n- **Dependency Injection（依賴注入）**：如 Dagger、Hilt。\r\n- **Jetpack**：Android 開發函式庫集合。\r\n- **Lifecycle**：管理元件生命週期。\r\n- **WorkManager**：排程背景任務。\r\n- **Navigation**：Fragment 導航。\r\n\r\n## 5. 除錯與建構相關\r\n- **Logcat**：日誌工具。\r\n- **Debugger**：中斷點除錯。\r\n- **Lint**：靜態程式碼分析。\r\n- **ProGuard/R8**：程式碼混淆最佳化。\r\n- **Build Variant**：建構變體，如 debug、release。\r\n- **Flavor**：產品變體，如免費版、付費版。\r\n- **Keystore**：簽章金鑰。\r\n\r\n## 6. 其他常見術語\r\n- **Context**：提供應用程式環境資訊。\r\n- **Permissions（權限）**：如儲存、相機。\r\n- **Thread（執行緒）**：主執行緒、副執行緒。\r\n- **Handler**：執行緒間通訊。\r\n- **Looper**：訊息迴圈。\r\n- **AsyncTask**：非同步任務（已淘汰）。\r\n- **SharedPreferences**：鍵值對儲存。\r\n- **Parcel**：序列化資料容器。\r\n- **ART**：Android 執行時。\r\n- **Dalvik**：早期虛擬機（已淘汰）。\r\n", "author": "Admin", "readTime": "5 min read"}, {"slug": "ChatGPT-Plus-User-Guide", "title": "ChatGPT Plus 使用指南", "excerpt": "全面介紹 ChatGPT Plus 的模型選擇、提問技巧與注意事項，助你高效使用 AI。", "date": "2025-05-14T00:00:00.000Z", "category": "教學", "tags": ["ChatGPT", "Plus", "使用指南"], "content": "\r\n# ChatGPT Plus User Guide\r\n此指南將幫助您更高效地使用 ChatGPT Plus，包括如何選擇模型、提問技巧以及一些注意事項。\r\n\r\n## 1. 選擇模型\r\n在當前版本的 ChatGPT Plus 中，您可以選擇以下幾種模型：\r\n\r\n### 1.1 模型選項\r\n+ **GPT-4o**  \r\n適用於大多數問題，提供廣泛的應用和較強的推理能力。適合一般問題、分析性任務和創意寫作等。\r\n+ **GPT-4o with scheduled tasks (BETA)**  \r\n用於安排任務，讓 ChatGPT 後續跟進並執行特定任務。適合需要定期追蹤或需要多次互動的任務。\r\n+ **o1**  \r\n使用高級推理，適合複雜的邏輯問題、需要深度推理的任務，如哲學性或數學問題分析。\r\n+ **o3-mini**  \r\n專注於快速且高級的推理，適合時間較緊或對響應速度要求較高的任務。雖然它依然具有強大的推理能力，但通常更適合日常問題解答和較簡單的任務。\r\n+ **o3-mini-high**  \r\n適合需要高效編程和邏輯推理的任務，尤其在代碼生成、調試以及解決與技術相關的問題時表現優秀。對於編程問題或算法分析，o3-mini-high 是最合適的選擇。\r\n\r\n### 1.2 何時選擇哪個模型\r\n+ **選擇 GPT-4o**  \r\n當您有一般性問題時，選擇此模型。它能處理廣泛的任務，包括日常問題解答、內容創作等。\r\n+ **選擇 GPT-4o with scheduled tasks**  \r\n當您的問題需要多次互動或後續跟進時，選擇此模型。它適合處理需要在未來進行補充和跟進的複雜問題。\r\n+ **選擇 o1**  \r\n如果您的問題涉及深度推理、複雜的推理鏈或專業分析，選擇此模型會得到更高質量的回答。\r\n+ **選擇 o3-mini**  \r\n如果您的任務較簡單，且希望快速得到答案，o3-mini 是最佳選擇。它提供較為快速的推理，適合日常問答。\r\n+ **選擇 o3-mini-high**  \r\n對於需要編程、算法分析或技術問題解決的用戶，o3-mini-high 是最適合的模型。它在處理技術細節和編程任務時表現出色。\r\n\r\n## 2. 提問技巧\r\n### 2.1 避免將多個問題集中在一個對話中\r\n+ **分開提問**：將多個不同問題拆分為獨立的提問。這樣可以幫助 ChatGPT 更加清晰地回答每個問題。\r\n    - **拆分問題示例**：\r\n        * 問題 1: `如何編寫Python代碼？`\r\n        * 問題 2: `如何準備演講？`\r\n\r\n### 2.2 使用清晰、簡潔的表述\r\n+ **簡潔明了**：確保您的問題簡單直接，避免冗長或含糊不清的描述。例如：“如何寫報告？”比“我該如何開始寫一份關於商業計劃的報告？”更簡潔明了。\r\n+ **指定細節**：如果需要特定格式的答案，提前說明。例如：“請以步驟列表的形式回答。”\r\n\r\n### 2.3 提供足夠的背景信息\r\n+ **具體背景**：提供問題相關的背景信息，幫助 ChatGPT 更好地理解您的需求，提供準確的答案。\r\n    - 例如：“我是一名Python初學者，如何寫一個簡單的計算器程序？”\r\n\r\n### 2.4 逐步提問，獲取詳細信息\r\n+ **分步提問**：將複雜問題分解為多個小問題，逐步獲取詳細的解答。\r\n    - 例如：如何開始規劃一個新項目？然後再詢問如何分配任務、如何評估項目進度。\r\n\r\n### 2.5 避免過於開放性問題\r\n+ **明確目標**：避免提出過於開放的問題。清楚地表達您的需求，可以得到更精準的回答。\r\n    - 例如：“給我推薦一些適合初學者的有趣編程項目。”\r\n\r\n### 2.6 利用多輪對話進行修正和澄清\r\n+ **逐步澄清**：如果模型的回答不完全符合您的需求，可以通過進一步提問澄清。例如：“你剛才提到的方法不適合我，能否給我一個更簡單的方案？”\r\n\r\n### 2.7 明確回答的期望形式\r\n+ **指定格式**：提前說明您希望回答的格式，如列表、段落或表格。\r\n    - 例如：“請用五個步驟列出如何提高工作效率。”\r\n\r\n### 2.8 避免過度複雜的問題\r\n+ **簡化問題**：將複雜問題拆解成多個簡單的部分，每個部分逐一詢問，避免一次性提出過多複雜問題。\r\n\r\n## 3. 注意事項\r\n### 3.1 確保邏輯清晰\r\n+ 在提問時，確保問題沒有邏輯混亂或矛盾，避免模糊不清的提問，這會導致答案不準確。\r\n\r\n### 3.2 模型限制\r\n+ **準確性**：儘管 GPT-4o 比 GPT-3.5 更強大，但它仍然可能提供不準確的回答。對於複雜任務，建議使用更強的模型（如 o1）。\r\n+ **上下文限制**：每個模型都有上下文限制。對於長對話或複雜任務，定期總結和回顧之前的對話內容，以保持一致性。\r\n\r\n### 3.3 適應模型的處理速度\r\n+ **響應速度**：不同模型的響應速度可能不同。對於時間緊迫的任務，可以選擇 **o3-mini** 以獲得較快的響應，而對於需要深度推理的任務，選擇 **o1** 或 **GPT-4o** 會更合適。\r\n\r\n通過以上建議，您可以更高效地使用 ChatGPT Plus，獲得更加精準和有價值的回答。選擇合適的模型，掌握提問技巧，讓每一次互動都更加順暢和高效。\r\n\r\n\r\n\r\n即買即用 ChatGPT，請訪問以下網站訂閱：\r\n\r\n[https://www.anmody.com](https://www.anmody.com)\r\n", "author": "Admin", "readTime": "5 min read"}, {"slug": "first-openai-api-chatgpt-conversation", "title": "第一次用 OpenAI API 聊天", "excerpt": "跟我學點樣用 OpenAI API 聊天！", "date": "2025-04-22T00:00:00.000Z", "category": "教學", "tags": ["OpenAI", "ChatGPT"], "content": "\r\n最簡單嘅方法，就係使用 `curl` 命令行工具，向 OpenAI API 發送請求。\r\n\r\nCode 示例：\r\n\r\n```bash\r\ncurl https://api.openai.com/v1/responses \\\r\n    -H \"Content-Type: application/json\" \\\r\n    -H \"Authorization: Bearer $OPENAI_API_KEY\" \\\r\n    -d '{\r\n        \"model\": \"gpt-4.1\",\r\n        \"input\": \"Write a one-sentence bedtime story about a unicorn.\"\r\n    }'\r\n```\r\n\r\n其中，`$OPENAI_API_KEY` 係你嘅 API KEY，你需要去 [https://platform.openai.com](https://platform.openai.com) Create an API key，然後複製貼上。\r\n\r\n通常在 PowerShell 或者 Bash 中使用 Curl。\r\n\r\n\r\n需要購買 OpenAI API KEY？\r\nContact us at [https://t.afffun.com/cmvp88bt](https://t.afffun.com/cmvp88bt)\r\n\r\n價格：\r\n\r\nHKD120 >>> USD$8\r\n\r\nHKD200 >>> USD$15\r\n\r\nHKD500 >>> USD$45\r\n\r\nHKD1000>>>USD$100\r\n\r\n💰 API 扣費標準遵循 OpenAI 價目表：\r\n[https://www.openai.com/api/pricing](https://www.openai.com/api/pricing)\r\n\r\n\r\n💕 歡迎加入我哋嘅教學頻道（WhatsAPP）：\r\n[https://t.afffun.com/av7rd3fg](https://t.afffun.com/av7rd3fg)\r\n", "author": "Admin", "readTime": "5 min read"}, {"slug": "intro-openai-api", "title": "初步認識 OpenAI API", "excerpt": "用最簡單嘅方式，帶你了解 OpenAI API 嘅基本名詞同使用原理。", "date": "2025-04-22T00:00:00.000Z", "category": "教學", "tags": ["OpenAI", "API"], "content": "\n## 乜嘢係 OpenAI API？\n\nOpenAI API 係由 OpenAI 提供嘅一組網絡介面（API），等你可以用程式方式連接同使用 OpenAI 嘅人工智能服務，例如 ChatGPT、GPT-4o、o3、o4、DALL·E 等。\n\n## 常見名詞解釋\n\n- **API KEY**  \n  API KEY 就係你用嚟認證身份嘅密鑰，好似你嘅會員證一樣。每次用 API 時都要帶住呢個 KEY，OpenAI 先會識得你係邊個同埋計算用量。\n\n- **BASE_URL**  \n  BASE_URL 係 API 嘅主網址，所有請求都要發去呢個網址。例如官方 API 嘅 BASE_URL 係 `https://api.openai.com/v1/`，如果你用代理或者第三方服務，BASE_URL 可能會唔同。\n\n- **Endpoint**  \n  Endpoint 係 API 入面唔同功能嘅路徑，例如 `/chat/completions` 係用嚟同 ChatGPT 對話嘅 endpoint。依家仲有一個新嘅路徑 `/v1/responses` ，兩者大致相同，而目前官方建議使用後者。\n\n- **Token**  \n  Token 係計算你輸入同輸出文字長度嘅單位，API 收費都係根據 token 數量計算。\n\n## OpenAI API 使用原理\n\n1. 你可以向我哋購買 API 額度，由我們創建 API KEY 畀你或你自己創建一個 API KEY。\n2. 用你嘅 API KEY 同 BASE_URL，喺程式（例如 Python、JavaScript）入面發送請求（request）去 OpenAI API。\n3. API 會根據你嘅請求（例如輸入一段文字），返回對應嘅 AI 回應。\n4. 每次請求都會消耗 token，OpenAI 會根據 token 數量收費。\n\n## 小貼士\n\n- API KEY 千祈唔好公開畀人睇，否則有機會被濫用。\n- 如果你用第三方服務，記得確認 BASE_URL 同 API KEY 嘅安全性。\n- 建議新手可以用 Postman 或 curl 測試 API，熟悉流程。\n\n希望呢篇文章可以幫你打好 OpenAI API 嘅基礎，有咩問題歡迎留言或聯絡我哋！\n\nContact us at:\n\n[https://t.afffun.com/cmvp88bt](https://t.afffun.com/cmvp88bt)\n", "author": "Admin", "readTime": "5 min read"}, {"slug": "quickstart", "title": "開發者快速入門", "excerpt": "用最簡單的方式，帶你認識 OpenAI API 的基本名詞與使用原理。", "date": "2025-04-22T00:00:00.000Z", "category": "教學", "tags": ["OpenAI", "快速入門"], "content": "\r\n開發者快速入門\r\n====================\r\n\r\n踏出使用 OpenAI API 的第一步。\r\n\r\nOpenAI API 提供簡單易用的介面，讓你能夠存取最先進的 AI 模型，用於文字生成、自然語言處理、電腦視覺等多種應用。以下範例展示如何像 ChatGPT 一樣，根據提示生成文字內容。\r\n\r\n### 文字生成範例\r\n\r\n- JavaScript Code\r\n\r\n```javascript\r\nimport OpenAI from \"openai\";\r\nconst client = new OpenAI();\r\n\r\nconst response = await client.responses.create({\r\n    model: \"gpt-4.1\",\r\n    input: \"請用一句話寫一個有關獨角獸的睡前故事。\"\r\n});\r\n\r\nconsole.log(response.output_text);\r\n```\r\n\r\n- Python Code\r\n\r\n```python\r\nfrom openai import OpenAI\r\nclient = OpenAI()\r\n\r\nresponse = client.responses.create(\r\n    model=\"gpt-4.1\",\r\n    input=\"請用一句話寫一個有關獨角獸的睡前故事。\"\r\n)\r\n\r\nprint(response.output_text)\r\n```\r\n\r\n- Bash Code\r\n\r\n```bash\r\ncurl \"https://api.openai.com/v1/responses\" \\\r\n    -H \"Content-Type: application/json\" \\\r\n    -H \"Authorization: Bearer $OPENAI_API_KEY\" \\\r\n    -d '{\r\n        \"model\": \"gpt-4.1\",\r\n        \"input\": \"請用一句話寫一個有關獨角獸的睡前故事。\"\r\n    }'\r\n```\r\n\r\n---\r\n\r\n### 回應資料保存政策\r\n\r\nAPI 回應物件預設會保存 30 天。你可以在 API 後台日誌頁面查看，或透過 API 查詢。如不希望保存，可在建立 Response 時設 `store: false`。\r\n\r\n---\r\n\r\n### 設定開發環境\r\n\r\n安裝並設定官方 OpenAI SDK，即可執行上述程式碼。\r\n\r\n---\r\n\r\n### 圖像分析範例\r\n\r\n你也可以將圖片作為輸入，讓模型進行分析。例如掃描收據、分析截圖、辨識現實世界物件等。\r\n\r\n- JavaScript Code\r\n\r\n```javascript\r\nimport OpenAI from \"openai\";\r\nconst client = new OpenAI();\r\n\r\nconst response = await client.responses.create({\r\n    model: \"gpt-4.1\",\r\n    input: [\r\n        { role: \"user\", content: \"這張照片是哪兩支球隊在比賽？\" },\r\n        {\r\n            role: \"user\",\r\n            content: [\r\n                {\r\n                    type: \"input_image\", \r\n                    image_url: \"https://upload.wikimedia.org/wikipedia/commons/3/3b/LeBron_James_Layup_%28Cleveland_vs_Brooklyn_2018%29.jpg\",\r\n                }\r\n            ],\r\n        },\r\n    ],\r\n});\r\n\r\nconsole.log(response.output_text);\r\n```\r\n\r\n---\r\n\r\n### 擴展模型能力：工具與網路搜尋\r\n\r\n你可以讓模型使用工具，例如網路搜尋，獲取最新資訊。\r\n\r\n- JavaScript Code\r\n\r\n```javascript\r\nimport OpenAI from \"openai\";\r\nconst client = new OpenAI();\r\n\r\nconst response = await client.responses.create({\r\n    model: \"gpt-4.1\",\r\n    tools: [ { type: \"web_search_preview\" } ],\r\n    input: \"請提供今天一則正面的新聞。\",\r\n});\r\n\r\nconsole.log(response.output_text);\r\n```\r\n\r\n---\r\n\r\n### 高效串流體驗\r\n\r\n你可以使用 [Realtime API](https://platform.openai.com/docs/guides/realtime) 或 [串流事件](https://platform.openai.com/docs/guides/streaming-responses) 實現低延遲、高效能的 AI 體驗。\r\n\r\n- JavaScript Code\r\n\r\n```javascript\r\nimport { OpenAI } from \"openai\";\r\nconst client = new OpenAI();\r\n\r\nconst stream = await client.responses.create({\r\n    model: \"gpt-4.1\",\r\n    input: [\r\n        {\r\n            role: \"user\",\r\n            content: \"請快速重複說十次『泡泡浴』。\",\r\n        },\r\n    ],\r\n    stream: true,\r\n});\r\n\r\nfor await (const event of stream) {\r\n    console.log(event);\r\n}\r\n```\r\n\r\n---\r\n\r\n### 建立智能代理（Agent）\r\n\r\n你可以利用 OpenAI 平台建立能夠自動執行任務的[智能代理](https://platform.openai.com/docs/guides/agents)。\r\n\r\n- Python Code\r\n\r\n```python\r\nfrom agents import Agent, Runner\r\nimport asyncio\r\n\r\nspanish_agent = Agent(\r\n    name=\"西班牙語代理\",\r\n    instructions=\"你只能用西班牙語回答。\",\r\n)\r\n\r\nenglish_agent = Agent(\r\n    name=\"英語代理\",\r\n    instructions=\"你只能用英語回答。\",\r\n)\r\n\r\ntriage_agent = Agent(\r\n    name=\"分流代理\",\r\n    instructions=\"根據請求語言分派給合適的代理。\",\r\n    handoffs=[spanish_agent, english_agent],\r\n)\r\n\r\nasync def main():\r\n    result = await Runner.run(triage_agent, input=\"Hola, ¿cómo estás?\")\r\n    print(result.final_output)\r\n\r\nif __name__ == \"__main__\":\r\n    asyncio.run(main())\r\n```\r\n", "author": "Admin", "readTime": "5 min read"}, {"slug": "An<PERSON><PERSON>_Plugin_USER_MANUAL_ZIP", "title": "Conversation Management 擴展程式用戶手冊 (ZIP 版本)", "excerpt": "專為需要使用 ZIP 安裝方式嘅用家而設，提供最詳細嘅 Conversation Management 擴展程式安裝同使用指南，包括開發者模式設定、故障排除等實用資訊！", "date": "2025-01-15T00:00:00.000Z", "category": "用戶指南", "tags": ["Chrome 擴展", "安裝指南", "對話管理", "ZIP 安裝", "開發者模式"], "content": "\r\n# Conversation Management 擴展程式用戶手冊 (ZIP 版本)\r\n\r\n![Conversation Management 擴展程式](/images/Conversation-Management_2025-05-30_001137_988.png)\r\n\r\n## 🌍 系統兼容性\r\n\r\n### 支援平台\r\n- ✅ Windows 10/11\r\n- ✅ macOS (Intel & Apple Silicon)\r\n- ✅ Linux (Ubuntu, CentOS 等)\r\n- ✅ ChromeOS\r\n\r\n### 支援瀏覽器\r\n- ✅ Google Chrome (推薦)\r\n- ✅ Microsoft Edge (Chromium 版本)\r\n- ✅ Brave Browser\r\n- ✅ 其他基於 Chromium 的瀏覽器\r\n\r\n## 📦 ZIP 版本安裝指南\r\n\r\n### 第一步：下載和解壓\r\n\r\n1. **下載 ZIP 檔案**\r\n   - 下載最新版本的 Conversation Management 擴展程式 ZIP 檔案： \r\n      [conversation-management-v1.2.3.zip](/downloads/conversation-management-v1.2.3-b040d51d.zip) 到您的電腦\r\n   - 建議儲存到桌面或專門的資料夾\r\n\r\n2. **解壓檔案**\r\n   - 右鍵點擊 ZIP 檔案\r\n   - 選擇「解壓縮到...」或「Extract to...」\r\n   - 選擇一個永久位置（重要：不要刪除此資料夾）\r\n\r\n### 第二步：開啟開發者模式\r\n\r\n1. **開啟 Chrome 瀏覽器**\r\n2. **進入擴展程式頁面**\r\n   - 在網址列輸入：`chrome://extensions/`\r\n   - 或點擊右上角三點選單 → 更多工具 → 擴展程式\r\n3. **啟用開發者模式**\r\n   - 在頁面右上角找到「開發者模式」開關\r\n   - 點擊開啟（開關變為藍色）\r\n\r\n### 第三步：載入擴展程式\r\n\r\n1. **點擊「載入未封裝項目」**\r\n   - 在開發者模式開啟後會出現此按鈕\r\n   - 英文版本為「Load unpacked」\r\n\r\n2. **選擇解壓後的資料夾**\r\n   - 瀏覽到您解壓 ZIP 檔案的位置\r\n   - 選擇包含 `manifest.json` 的資料夾\r\n   - 點擊「選擇資料夾」或「Select Folder」\r\n\r\n3. **確認安裝成功**\r\n   - 擴展程式會出現在擴展程式列表中\r\n   - 狀態顯示為「已啟用」\r\n   - 瀏覽器工具列會出現擴展程式圖示\r\n\r\n## 🔧 功能使用說明\r\n\r\n### 支援的網站\r\n- ✅ [Anmody](https://ip.anmody.com)\r\n\r\n### 基本操作流程\r\n\r\n1. **訪問支援的網站**\r\n   - 開啟任意支援的對話網站\r\n   - 擴展程式會自動進行域名驗證\r\n\r\n2. **開啟擴展程式面板**\r\n   - 點擊瀏覽器工具列中的擴展程式圖示\r\n   - 擴展程式會自動檢測當前網站是否支援\r\n\r\n3. **載入對話列表**\r\n   - 點擊「重新整理列表」按鈕\r\n   - 等待系統載入您的對話記錄\r\n\r\n4. **選擇要匯出的對話**\r\n   - 勾選您想要匯出的對話（支援多選）\r\n   - 可以使用「全選」功能快速選擇\r\n\r\n5. **選擇匯出格式**\r\n   - **HTML 格式**：美觀的網頁格式，適合檢視和分享\r\n   - **Markdown 格式**：適合技術文件和進一步編輯\r\n   - **純文字格式**：簡潔的文字格式，相容性最佳\r\n   - **暫不支援圖像匯出**：圖像可使用普通保存 / Download 方式\r\n\r\n6. **執行匯出**\r\n   - 點擊「匯出選中對話」按鈕\r\n   - 檔案會自動下載到您的下載資料夾\r\n\r\n## ⚠️ 重要注意事項\r\n\r\n### ZIP 版本特殊注意事項\r\n- 🚨 **不要刪除解壓的資料夾**：刪除後擴展程式會失效\r\n- 🚨 **擴展程式 ID 可能變化**：這是開發者模式的特性\r\n- 🚨 **需要保持開發者模式開啟**：關閉後擴展程式會被停用\r\n\r\n### 一般使用注意事項\r\n- 🔒 **域名限制**：擴展程式僅在授權域名上運作\r\n- 🌐 **網路要求**：需要穩定的網路連線進行域名驗證\r\n- 🔄 **故障排除**：如遇問題，請重新整理頁面後重試\r\n- 📱 **瀏覽器相容**：僅支援 Chrome 及基於 Chromium 的瀏覽器\r\n\r\n## 🛡️ 安全與隱私\r\n\r\n- ✅ **域名驗證保護**：透過遠端伺服器驗證，防止未授權使用\r\n- ✅ **原始碼混淆**：核心程式碼經過高級混淆處理\r\n- ✅ **隱私保護**：不收集、儲存或傳輸任何個人對話資料\r\n- ✅ **本地處理**：所有對話資料僅在本地瀏覽器中處理\r\n\r\n## 🚨 常見問題排除\r\n\r\n### Q: 擴展程式顯示「域名未授權」\r\n**A:** 請確認您在支援的網站上使用擴展程式，並檢查網路連線。\r\n\r\n### Q: 無法載入對話列表\r\n**A:** 請重新整理頁面，確保已登入對話網站，然後重試。\r\n\r\n### Q: 匯出的檔案為空\r\n**A:** 請確認已選擇對話，且對話包含有效內容。\r\n\r\n### Q: 擴展程式圖示顯示為灰色\r\n**A:** 這表示當前網站不受支援，請訪問支援的對話網站。\r\n\r\n### Q: 擴展程式突然消失或停用\r\n**A:** 檢查是否意外關閉了開發者模式，或刪除了解壓的資料夾。\r\n\r\n## 📞 技術支援\r\n\r\n如果遇到其他問題，請檢查：\r\n1. Chrome 瀏覽器版本是否為最新\r\n2. 擴展程式是否已正確安裝並啟用\r\n3. 開發者模式是否保持開啟\r\n4. 解壓的資料夾是否完整存在\r\n5. 網路連線是否正常\r\n\r\n如問題仍然存在，請聯絡技術支援並提供詳細的錯誤資訊。\r\n\r\n---\r\n\r\n## 🇺🇸 English Version\r\n\r\n### System Compatibility\r\n\r\n**Supported Platforms:**\r\n- ✅ Windows 10/11\r\n- ✅ macOS (Intel & Apple Silicon)\r\n- ✅ Linux (Ubuntu, CentOS, etc.)\r\n- ✅ ChromeOS\r\n\r\n**Supported Browsers:**\r\n- ✅ Google Chrome (Recommended)\r\n- ✅ Microsoft Edge (Chromium version)\r\n- ✅ Brave Browser\r\n- ✅ Other Chromium-based browsers\r\n\r\n### ZIP Version Installation Guide\r\n\r\n**Step 1: Download and Extract**\r\n1. Download [conversation-management-v1.2.3.zip](/downloads/conversation-management-v1.2.3-b040d51d.zip) to your computer\r\n2. Right-click the ZIP file and select \"Extract to...\"\r\n3. Choose a permanent location (Important: Do not delete this folder)\r\n\r\n**Step 2: Enable Developer Mode**\r\n1. Open Chrome browser\r\n2. Navigate to `chrome://extensions/`\r\n3. Toggle \"Developer mode\" ON (top-right corner)\r\n\r\n**Step 3: Load Extension**\r\n1. Click \"Load unpacked\" button\r\n2. Select the extracted folder containing `manifest.json`\r\n3. Confirm installation success\r\n\r\n### Usage Instructions\r\n\r\n**Supported Websites:**\r\n- ✅ [Anmody](https://ip.anmody.com)\r\n\r\n**Basic Operations:**\r\n1. Visit supported websites\r\n2. Click extension icon in browser toolbar\r\n3. Click \"Refresh List\" to load conversations\r\n4. Select conversations to export (multiple selection supported)\r\n5. Choose export format: HTML, Markdown, or Plain Text\r\n6. Click \"Export Selected Conversations\"\r\n\r\n### Important Notes for ZIP Version\r\n\r\n- 🚨 **Do not delete the extracted folder** - Extension will stop working\r\n- 🚨 **Extension ID may change** - This is a developer mode characteristic\r\n- 🚨 **Keep developer mode enabled** - Extension will be disabled if turned off\r\n\r\n### Security & Privacy\r\n\r\n- ✅ Domain validation protection\r\n- ✅ Source code obfuscation\r\n- ✅ No personal data collection\r\n- ✅ Local processing only\r\n\r\n### Troubleshooting\r\n\r\n**Q: Extension shows \"Domain not authorized\"**\r\nA: Ensure you're on a supported website and check network connection.\r\n\r\n**Q: Cannot load conversation list**\r\nA: Refresh the page, ensure you're logged in, then retry.\r\n\r\n**Q: Exported file is empty**\r\nA: Confirm conversations are selected and contain valid content.\r\n\r\n**Q: Extension icon appears grayed out**\r\nA: Current website is not supported. Visit supported conversation websites.\r\n\r\n### Technical Support\r\n\r\nIf you encounter issues, please check:\r\n1. Chrome browser is up to date\r\n2. Extension is properly installed and enabled\r\n3. Developer mode remains enabled\r\n4. Extracted folder exists and is complete\r\n5. Network connection is stable\r\n\r\nFor persistent issues, contact technical support with detailed error information.\r\n", "author": "Admin", "readTime": "5 min read"}, {"slug": "site", "title": "關於本站", "excerpt": "本站專為香港用家而設，提供最實用嘅 OpenAI API 教學同資源，等你輕鬆學識 AI 技術，快人一步！", "date": "2025-01-01T00:00:00.000Z", "category": "站務公告", "tags": ["關於", "站務"], "content": "\n## 關於本站\n\n呢個網站係專為香港同華語地區嘅朋友而設，目標係用最貼地、易明嘅方式，教大家點樣用 OpenAI API。無論你係初學者定有經驗嘅開發者，都可以搵到啱用嘅教學同資源。\n\n### 主要內容\n\n- OpenAI API 基本用法同進階技巧\n- 常見問題解答（FAQ）\n- 實用程式碼例子同真實專案分享\n- 最新公告同功能更新\n\n### 適合邊啲人？\n\n只要你對 AI、OpenAI API 有興趣，或者想搵資源提升自己，都歡迎嚟學習。唔理你係學生、工程師、創業者，定係純粹好奇，都啱用！\n\n### 聯絡方法\n\n如果你需要購買 API KEY，或者有咩建議、查詢或者合作想法，歡迎隨時搵我哋：\n[https://t.afffun.com/cmvp88bt](https://t.afffun.com/cmvp88bt)\n\n### 訂閱 ChatGPT Plus (非 API 形式)\n\n如果你期望使用 Web 版本 ChatGPT 而非 API ，亦可入我們嘅 Store 訂閱 ChatGPT Plus Share Account Plan，勁抵玩，只需要 HK$85 per month：\n\n24小時自動運作，幾時買都得\n[https://www.anmody.com](https://www.anmody.com)\n\n多謝大家支持，記得 bookmark 埋本站，定期返嚟睇下有咩新內容！\n", "author": "Admin", "readTime": "5 min read"}]