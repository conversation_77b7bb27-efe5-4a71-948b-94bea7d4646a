// 批量为所有 Markdown 文章 frontmatter 增加 slug 字段（tags_slug/category_slug）
// 用于历史数据迁移到 slug 路由方案
// 用法：node scripts/batch-slug-migrate.js

const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

// 复用 lib/tags.ts 的 slugify 逻辑（如需更优可引入 pinyin 库）
function slugify(str) {
  return str
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/\s+/g, '-')
    .replace(/[_·•—–—\u2014\u2013]/g, '-')
    .replace(/[^\w\u4e00-\u9fa5-]/g, '')
    .replace(/[\u4e00-\u9fa5]+/g, m => Array.from(m).map(c => 'zh').join('-'))
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase();
}

const articlesDir = path.join(__dirname, '../data/articles');
const files = fs.readdirSync(articlesDir).filter(f => f.endsWith('.md'));

files.forEach(file => {
  const filePath = path.join(articlesDir, file);
  const raw = fs.readFileSync(filePath, 'utf-8');
  const parsed = matter(raw);
  let changed = false;

  // tags_slug
  if (parsed.data.tags && Array.isArray(parsed.data.tags)) {
    const tagsSlug = parsed.data.tags.map(t => slugify(t));
    if (JSON.stringify(parsed.data.tags_slug) !== JSON.stringify(tagsSlug)) {
      parsed.data.tags_slug = tagsSlug;
      changed = true;
    }
  }
  // category_slug
  if (parsed.data.category) {
    const catSlug = slugify(parsed.data.category);
    if (parsed.data.category_slug !== catSlug) {
      parsed.data.category_slug = catSlug;
      changed = true;
    }
  }
  if (changed) {
    const newContent = matter.stringify(parsed.content, parsed.data);
    fs.writeFileSync(filePath, newContent, 'utf-8');
    console.log(`Updated: ${file}`);
  }
});

console.log('批量 slug 化迁移完成。');
