---
title: 開發者快速入門
date: 2025-04-22T00:00:00.000Z
excerpt: 用最簡單的方式，帶你認識 OpenAI API 的基本名詞與使用原理。
category: 教學
tags:
  - OpenAI
  - 快速入門
article_id: 4
tags_slug:
  - openai
  - quick-start
category_slug: tutorials
---

開發者快速入門
====================

踏出使用 OpenAI API 的第一步。

OpenAI API 提供簡單易用的介面，讓你能夠存取最先進的 AI 模型，用於文字生成、自然語言處理、電腦視覺等多種應用。以下範例展示如何像 ChatGPT 一樣，根據提示生成文字內容。

### 文字生成範例

- JavaScript Code

```javascript
import OpenAI from "openai";
const client = new OpenAI();

const response = await client.responses.create({
    model: "gpt-4.1",
    input: "請用一句話寫一個有關獨角獸的睡前故事。"
});

console.log(response.output_text);
```

- Python Code

```python
from openai import OpenAI
client = OpenAI()

response = client.responses.create(
    model="gpt-4.1",
    input="請用一句話寫一個有關獨角獸的睡前故事。"
)

print(response.output_text)
```

- Bash Code

```bash
curl "https://api.openai.com/v1/responses" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $OPENAI_API_KEY" \
    -d '{
        "model": "gpt-4.1",
        "input": "請用一句話寫一個有關獨角獸的睡前故事。"
    }'
```

---

### 回應資料保存政策

API 回應物件預設會保存 30 天。你可以在 API 後台日誌頁面查看，或透過 API 查詢。如不希望保存，可在建立 Response 時設 `store: false`。

---

### 設定開發環境

安裝並設定官方 OpenAI SDK，即可執行上述程式碼。

---

### 圖像分析範例

你也可以將圖片作為輸入，讓模型進行分析。例如掃描收據、分析截圖、辨識現實世界物件等。

- JavaScript Code

```javascript
import OpenAI from "openai";
const client = new OpenAI();

const response = await client.responses.create({
    model: "gpt-4.1",
    input: [
        { role: "user", content: "這張照片是哪兩支球隊在比賽？" },
        {
            role: "user",
            content: [
                {
                    type: "input_image", 
                    image_url: "https://upload.wikimedia.org/wikipedia/commons/3/3b/LeBron_James_Layup_%28Cleveland_vs_Brooklyn_2018%29.jpg",
                }
            ],
        },
    ],
});

console.log(response.output_text);
```

---

### 擴展模型能力：工具與網路搜尋

你可以讓模型使用工具，例如網路搜尋，獲取最新資訊。

- JavaScript Code

```javascript
import OpenAI from "openai";
const client = new OpenAI();

const response = await client.responses.create({
    model: "gpt-4.1",
    tools: [ { type: "web_search_preview" } ],
    input: "請提供今天一則正面的新聞。",
});

console.log(response.output_text);
```

---

### 高效串流體驗

你可以使用 [Realtime API](https://platform.openai.com/docs/guides/realtime) 或 [串流事件](https://platform.openai.com/docs/guides/streaming-responses) 實現低延遲、高效能的 AI 體驗。

- JavaScript Code

```javascript
import { OpenAI } from "openai";
const client = new OpenAI();

const stream = await client.responses.create({
    model: "gpt-4.1",
    input: [
        {
            role: "user",
            content: "請快速重複說十次『泡泡浴』。",
        },
    ],
    stream: true,
});

for await (const event of stream) {
    console.log(event);
}
```

---

### 建立智能代理（Agent）

你可以利用 OpenAI 平台建立能夠自動執行任務的[智能代理](https://platform.openai.com/docs/guides/agents)。

- Python Code

```python
from agents import Agent, Runner
import asyncio

spanish_agent = Agent(
    name="西班牙語代理",
    instructions="你只能用西班牙語回答。",
)

english_agent = Agent(
    name="英語代理",
    instructions="你只能用英語回答。",
)

triage_agent = Agent(
    name="分流代理",
    instructions="根據請求語言分派給合適的代理。",
    handoffs=[spanish_agent, english_agent],
)

async def main():
    result = await Runner.run(triage_agent, input="Hola, ¿cómo estás?")
    print(result.final_output)

if __name__ == "__main__":
    asyncio.run(main())
```
