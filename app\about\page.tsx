import type { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '關於我們 | Anmody AI 教學部落',
  description: '了解 Anmody AI 教學部落的使命、願景和團隊，我們致力於提供高質量的人工智慧技術教學內容。',
}

export default function AboutPage() {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 md:p-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">關於我們</h1>
        
        <div className="prose prose-lg max-w-none">
          <h2>我們的使命</h2>
          <p>
            Anmody AI 教學部落致力於為華語地區的開發者、學習者和 AI 愛好者提供最優質、最實用的人工智慧技術教學內容。我們相信，透過分享知識和經驗，可以幫助更多人掌握 AI 技術，並將其應用到實際工作和生活中。
          </p>

          <h2>我們的內容</h2>
          <p>我們的部落格涵蓋以下主要領域：</p>
          <ul>
            <li><strong>OpenAI API 教學</strong> - 深入淺出的 API 使用指南和最佳實踐</li>
            <li><strong>ChatGPT 應用開發</strong> - 實用的應用開發技巧和案例分析</li>
            <li><strong>AI 工具介紹</strong> - 最新 AI 工具的評測和使用教學</li>
            <li><strong>程式開發技巧</strong> - 與 AI 相關的程式設計技術分享</li>
            <li><strong>行業趨勢分析</strong> - AI 技術發展趨勢和市場洞察</li>
          </ul>

          <h2>我們的價值觀</h2>
          <ul>
            <li><strong>專業性</strong> - 確保所有內容都經過嚴格的技術驗證</li>
            <li><strong>實用性</strong> - 專注於提供可直接應用的知識和技能</li>
            <li><strong>易懂性</strong> - 用簡潔明瞭的語言解釋複雜的技術概念</li>
            <li><strong>時效性</strong> - 及時更新內容，跟上技術發展的步伐</li>
          </ul>

          <h2>聯絡我們</h2>
          <p>
            如果您有任何問題、建議或合作意向，歡迎透過以下方式聯絡我們：
          </p>
          <ul>
            <li>
              <strong>業務合作：</strong>
              <Link href="https://t.afffun.com/cmvp88bt" className="text-blue-600 hover:text-blue-700">
                https://t.afffun.com/cmvp88bt
              </Link>
            </li>
            <li>
              <strong>服務購買：</strong>
              <Link href="https://www.anmody.com" className="text-blue-600 hover:text-blue-700">
                https://www.anmody.com
              </Link>
            </li>
          </ul>

          <h2>免責聲明</h2>
          <p>
            本部落格提供的所有內容僅供學習和參考使用。我們努力確保內容的準確性，但不對因使用本站內容而產生的任何損失承擔責任。在實際應用中，請根據具體情況進行適當的測試和驗證。
          </p>

          <h2>版權聲明</h2>
          <p>
            本站所有原創內容均受版權保護。歡迎在註明出處的前提下分享和引用我們的內容。對於轉載或商業使用，請事先聯絡我們獲得授權。
          </p>

          <div className="mt-12 p-6 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">加入我們的學習社群</h3>
            <p className="text-blue-800 mb-4">
              想要獲得最新的 AI 技術資訊和教學內容？歡迎關注我們的更新！
            </p>
            <Link 
              href="/contact" 
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              聯絡我們
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
