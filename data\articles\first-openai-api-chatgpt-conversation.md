---
title: 第一次用 OpenAI API 聊天
date: 2025-04-22T00:00:00.000Z
excerpt: 跟我學點樣用 OpenAI API 聊天！
category: 教學
tags:
  - OpenAI
  - ChatGPT
article_id: 3
tags_slug:
  - openai
  - chatgpt
category_slug: tutorials
---

最簡單嘅方法，就係使用 `curl` 命令行工具，向 OpenAI API 發送請求。

Code 示例：

```bash
curl https://api.openai.com/v1/responses \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $OPENAI_API_KEY" \
    -d '{
        "model": "gpt-4.1",
        "input": "Write a one-sentence bedtime story about a unicorn."
    }'
```

其中，`$OPENAI_API_KEY` 係你嘅 API KEY，你需要去 [https://platform.openai.com](https://platform.openai.com) Create an API key，然後複製貼上。

通常在 PowerShell 或者 Bash 中使用 Curl。


需要購買 OpenAI API KEY？
Contact us at [https://t.afffun.com/cmvp88bt](https://t.afffun.com/cmvp88bt)

價格：

HKD120 >>> USD$8

HKD200 >>> USD$15

HKD500 >>> USD$45

HKD1000>>>USD$100

💰 API 扣費標準遵循 OpenAI 價目表：
[https://www.openai.com/api/pricing](https://www.openai.com/api/pricing)


💕 歡迎加入我哋嘅教學頻道（WhatsAPP）：
[https://t.afffun.com/av7rd3fg](https://t.afffun.com/av7rd3fg)
