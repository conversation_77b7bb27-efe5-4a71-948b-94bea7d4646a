import { getArticlesByTag, getAllArticles } from '@/lib/articles'
import { slugifyTag, getTagSlugAndOriginal } from '@/lib/tags'
import ArticleCard from '@/components/ArticleCard'
import Link from 'next/link'
import { Metadata } from 'next'

export async function generateMetadata({ params }: { params: Promise<{ tag: string }> }): Promise<Metadata> {
  const { tag } = await params
  const { original } = getTagSlugAndOriginal(tag)
  return {
    title: `#${original} | 文章標籤 | LearnMarts`,
    description: `瀏覽標籤 #${original} 下的所有 AI 技術教學文章。`,
    openGraph: {
      title: `#${original} | 文章標籤 | LearnMarts`,
      description: `瀏覽標籤 #${original} 下的所有 AI 技術教學文章。`,
      url: `https://learnmarts.com/tags/${slugifyTag(original)}`,
      type: 'website',
    },
    alternates: {
      canonical: `https://learnmarts.com/tags/${slugifyTag(original)}`,
    },
  }
}

export async function generateStaticParams() {
  const articles = getAllArticles()
  const allTags = articles.flatMap(article => article.tags || [])
  const uniqueTags = Array.from(new Set(allTags))
  
  // 为每个标签生成两种路径：原始标签和slug化标签
  const params: { tag: string }[] = []
  
  uniqueTags.forEach((tag) => {
    const slugified = slugifyTag(tag)
    
    // 添加slug化的版本（推荐使用）
    params.push({ tag: slugified })
    
    // 如果slug和原始标签不同，也添加原始编码版本作为兼容
    if (slugified !== tag) {
      params.push({ tag: encodeURIComponent(tag) })
    }
  })
  
  return params
}

export default async function TagPage({ params }: { params: Promise<{ tag: string }> }) {
  const { tag } = await params
  const { original } = getTagSlugAndOriginal(tag)
  const articles = getArticlesByTag(original)

  return (
    <div className="max-w-6xl mx-auto">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
        <Link href="/" className="hover:text-blue-600">首頁</Link>
        <span>/</span>
        <Link href="/tags" className="hover:text-blue-600">標籤</Link>
        <span>/</span>
        <span className="text-gray-900">#{original}</span>
      </nav>

      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">#{original}</h1>
        <p className="text-xl text-gray-600">
          共 {articles.length} 篇文章
        </p>
      </div>

      {articles.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {articles.map((article, index) => (
            <div
              key={article.slug}
              className={`transform hover:-translate-y-1 transition-all duration-300 article-item delay-${index % 6}`}
            >
              <ArticleCard {...article} />
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">此標籤下暫無文章</p>
          <Link 
            href="/articles" 
            className="inline-block mt-4 text-blue-600 hover:text-blue-700"
          >
            瀏覽所有文章
          </Link>
        </div>
      )}
    </div>
  )
}
