# 📝 Anmody AI 教学博客 - 文章创作指南

本指南将详细介绍如何为 Anmody AI 教学博客创建新文章，包括文件结构、格式要求、最佳实践和发布流程。

## 📁 项目结构概览

```
learnmarts.com/
├── data/articles/          # 📄 文章 Markdown 文件存放目录
├── docs/                   # 📚 项目文档
├── app/                    # 🎯 Next.js 应用页面
├── components/             # 🧩 React 组件
├── lib/                    # 📦 工具函数和数据处理
├── public/                 # 🌐 静态资源
└── scripts/                # 🔧 构建脚本
```

## 🚀 快速开始

### 方法一：使用自动化工具（推荐）

使用交互式文章创建工具，自动生成文章框架：

```bash
# 运行文章创建工具
npm run new-article
```

**工具功能**：
- 🤖 **自动生成文章ID**：获取下一个可用的唯一ID
- 📝 **智能文件命名**：根据标题自动生成规范的文件名
- 🏷️ **标签映射**：自动将中文标签转换为英文slug
- 📄 **模板填充**：自动生成完整的文章模板
- ✅ **格式验证**：确保所有必填字段正确填写

**使用步骤**：
1. 运行 `npm run new-article`
2. 按提示输入文章标题
3. 输入文章摘要（1-2句话）
4. 选择文章分类（1-3）
5. 输入标签（用逗号分隔）
6. 输入作者姓名（可选，默认为 Anmody）
7. 工具自动生成文章文件

**示例交互过程**：
```
🎉 欢迎使用文章创建工具！

📝 请输入文章标题: Kubernetes 入门指南
📄 请输入文章摘要 (1-2句话): 从零开始学习 Kubernetes 容器编排技术，包含安装、配置和部署实践。

📂 可选分类:
1. 教學 (技术教程)
2. 用戶指南 (产品使用说明)
3. 站務公告 (网站公告)
请选择分类 (1-3): 1

🏷️ 请输入标签 (用逗号分隔): Kubernetes, Docker, 容器化, DevOps
👤 作者姓名 (默认: Anmody):

✅ 文章创建成功！
📁 文件路径: data/articles/kubernetes-getting-started-guide.md
🆔 文章ID: 9
```

### 方法二：手动创建文章

#### 1. 创建新文章文件

在 `data/articles/` 目录下创建新的 `.md` 文件：

```bash
# 文件命名规范：使用英文，用连字符分隔，描述性强
# ✅ 好的命名示例：
docker-compose-deployment-guide.md
chatgpt-api-best-practices.md
kubernetes-getting-started.md

# ❌ 避免的命名：
article1.md
新文章.md
very-long-article-name-that-is-hard-to-read.md
```

#### 2. 使用文章模板

每篇文章都必须以 YAML front matter 开头：

```markdown
---
title: 文章标题（中文，简洁明了）
date: 2025-07-01T00:00:00.000Z
excerpt: 文章摘要，1-2句话概括文章内容，用于SEO和文章卡片显示
category: 教學
tags:
  - 标签1
  - 标签2
  - 标签3
article_id: 8
author: Anmody
readTime: 5 分鐘閱讀
tags_slug:
  - tag1-slug
  - tag2-slug
  - tag3-slug
category_slug: tutorials
---

# 文章标题

文章正文内容开始...
```

## 📋 Front Matter 字段详解

### 必填字段

| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `title` | String | 文章标题 | `"Docker Compose 现代化部署指南"` |
| `date` | ISO Date | 发布日期 | `2025-07-01T00:00:00.000Z` |
| `excerpt` | String | 文章摘要 | `"深入学习 Docker Compose V2..."` |
| `category` | String | 文章分类 | `"教學"` |
| `tags` | Array | 标签列表 | `["Docker", "DevOps"]` |
| `article_id` | Number | 唯一文章ID | `8` |

### 可选字段

| 字段 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `author` | String | 作者名称 | `"Anmody"` |
| `readTime` | String | 预估阅读时间 | `"5 分鐘閱讀"` |
| `tags_slug` | Array | 标签的英文slug | 自动生成 |
| `category_slug` | String | 分类的英文slug | 自动生成 |

## 🏷️ 分类和标签规范

### 现有分类

| 中文分类 | 英文Slug | 用途 |
|----------|----------|------|
| 教學 | tutorials | 技术教程和指南 |
| 用戶指南 | user-guide-cat | 产品使用说明 |
| 站務公告 | site-announcements | 网站公告和更新 |

### 标签命名规范

**技术类标签**：
- `OpenAI`, `ChatGPT`, `API`
- `Docker`, `Kubernetes`, `DevOps`
- `JavaScript`, `Python`, `Node.js`

**内容类型标签**：
- `快速入門`, `使用指南`, `最佳實踐`
- `故障排除`, `安全配置`, `性能優化`

**产品功能标签**：
- `Chrome 擴展`, `安裝指南`, `對話管理`

## ✍️ 内容编写指南

### 1. 文章结构建议

```markdown
# 主标题

简短的引言段落，介绍文章主题和读者将学到什么。

## 🎯 学习目标（可选）

- 目标1
- 目标2
- 目标3

## 📋 前置需求

列出读者需要的基础知识或工具。

## 🚀 正文内容

### 二级标题1

内容...

### 二级标题2

内容...

## 💡 最佳实践

提供实用建议和注意事项。

## 🔧 故障排除

常见问题和解决方案。

## 📚 总结

总结要点和下一步建议。

## 🔗 相关资源

- [相关链接1](URL)
- [相关链接2](URL)
```

### 2. 内容编写最佳实践

#### 📝 写作风格
- **语言**：使用繁体中文（香港）
- **语调**：专业但友好，避免过于技术化的术语
- **结构**：使用清晰的标题层次和列表
- **长度**：建议 1500-3000 字，阅读时间 5-10 分钟

#### 🎨 格式化技巧
```markdown
# 使用 Emoji 让标题更生动
## 🚀 部署指南
## 🔧 配置说明
## 💡 最佳实践

# 代码块要指定语言
```javascript
const example = "Hello World";
```

# 使用表格整理信息
| 参数 | 类型 | 说明 |
|------|------|------|
| name | String | 名称 |

# 使用引用块突出重要信息
> **重要提醒**：请确保使用 Docker Compose V2 而非旧版本。

# 使用列表组织内容
- ✅ 推荐做法
- ❌ 避免做法
- 💡 小贴士
```

### 3. 技术文章特殊要求

#### 代码示例
- 提供完整、可运行的代码
- 添加详细的注释
- 包含错误处理示例

#### 配置文件
- 提供完整的配置文件示例
- 解释每个配置项的作用
- 提供不同环境的配置变体

#### 命令行操作
```bash
# 为每个命令添加注释
docker compose up -d

# 提供预期输出示例
# 输出：
# Creating network "myapp_default" with the default driver
# Creating myapp_db_1 ... done
```

## 🤖 自动化工具详解

### npm run new-article 工具

这是一个交互式的文章创建工具，可以大大简化文章创建流程。

#### 工具特性

1. **智能ID管理**
   - 自动扫描现有文章，获取最大ID
   - 生成下一个可用的唯一ID
   - 避免ID冲突问题

2. **文件名生成**
   ```javascript
   // 示例：标题 "Docker Compose 部署指南"
   // 生成文件名：docker-compose-deployment-guide.md
   ```

3. **标签智能映射**
   ```javascript
   // 常用标签自动映射
   'Docker' → 'docker'
   'ChatGPT' → 'chatgpt'
   '快速入門' → 'quick-start'
   '使用指南' → 'user-guide'
   ```

4. **分类自动处理**
   ```javascript
   // 分类选择和slug映射
   '教學' → 'tutorials'
   '用戶指南' → 'user-guide-cat'
   '站務公告' → 'site-announcements'
   ```

#### 工具优势

| 特性 | 手动创建 | 自动化工具 |
|------|----------|------------|
| 创建时间 | 5-10分钟 | 1-2分钟 |
| 错误率 | 较高 | 极低 |
| ID管理 | 手动查找 | 自动生成 |
| 格式一致性 | 依赖经验 | 完全一致 |
| 学习成本 | 需要熟悉模板 | 交互式引导 |

#### 使用场景

**✅ 推荐使用自动化工具的情况**：
- 新手创作者
- 需要快速创建文章
- 希望避免格式错误
- 批量创建文章

**⚠️ 可能需要手动创建的情况**：
- 需要特殊的文章结构
- 复制现有文章进行修改
- 有特定的命名需求

### 工具源码说明

工具位于 `scripts/create-new-article.js`，主要功能模块：

```javascript
// 主要功能函数
getNextArticleId()        // 获取下一个可用ID
generateSlug(title)       // 生成文件名slug
generateTagSlug(tag)      // 生成标签slug
getCategorySlug(category) // 获取分类slug
generateArticleTemplate() // 生成文章模板
```

## 🔧 文章ID 管理

### 自动ID管理（推荐）

使用 `npm run new-article` 工具会自动处理ID分配，无需手动管理。

### 手动ID管理

如果需要手动创建文章，可以通过以下方式获取下一个可用ID：

1. **查看现有文章的最大ID**：
```bash
# 在项目根目录执行
grep -r "article_id:" data/articles/ | sort -n

# 或者使用工具函数
node -e "console.log(require('./scripts/create-new-article.js').getNextArticleId())"
```

2. **使用下一个连续数字作为新文章ID**

### ID 分配规则

- **1-999**：常规技术文章
- **1000-1999**：用户指南类文章
- **2000-2999**：产品功能介绍
- **3000+**：站务公告和特殊内容

### ID 冲突处理

如果遇到ID冲突：
1. 检查是否有重复的 article_id
2. 使用自动化工具重新生成
3. 手动调整冲突的ID

## 🏗️ 构建和发布流程

### 完整的文章发布流程

#### 方法一：使用自动化工具（推荐）

```bash
# 1. 创建新文章
npm run new-article
# 按提示填写文章信息，工具会自动生成文章文件

# 2. 编辑文章内容
# 使用您喜欢的编辑器编辑生成的 .md 文件

# 3. 本地预览
npm run dev
# 访问 http://localhost:3000 预览文章

# 4. 完整构建
npm run build

# 5. 本地测试构建结果
npx serve@latest out

# 6. 提交和部署
git add .
git commit -m "新增文章：[文章标题]"
git push origin main
```

#### 方法二：传统手动流程

```bash
# 1. 手动创建文章文件
cp docs/文章模板.md data/articles/your-article-name.md

# 2. 编辑文章内容和元数据
# 手动填写 front matter 和正文内容

# 3. 生成文章数据
npm run generate-articles-data

# 4. 启动开发服务器预览
npm run dev

# 5. 完整构建
npm run build

# 6. 本地预览构建结果
npx serve@latest out

# 7. 部署到生产环境
git add .
git commit -m "新增文章：[文章标题]"
git push origin main
```

### 构建命令详解

| 命令 | 功能 | 使用场景 |
|------|------|----------|
| `npm run new-article` | 创建新文章 | 开始写作新文章 |
| `npm run dev` | 开发服务器 | 实时预览和调试 |
| `npm run build` | 完整构建 | 准备发布 |
| `npm run generate-articles-data` | 生成文章数据 | 单独更新文章索引 |
| `npm run generate-rss` | 生成RSS | 单独更新RSS源 |
| `npm run generate-sitemap` | 生成站点地图 | 单独更新SEO |

### 本地开发和测试

#### 开发模式
```bash
# 启动开发服务器（支持热重载）
npm run dev

# 访问地址
http://localhost:3000

# 实时查看文章效果
http://localhost:3000/articles/your-article-slug/
```

#### 生产模式测试
```bash
# 完整构建
npm run build

# 本地预览生产版本
npx serve@latest out

# 测试所有功能
# - 文章页面显示
# - 分类和标签筛选
# - 搜索功能
# - RSS订阅
```

### 部署到生产环境

#### 自动部署（Cloudflare Pages）
```bash
# 提交代码到主分支
git add .
git commit -m "新增文章：Docker Compose 现代化部署指南"
git push origin main

# Cloudflare Pages 会自动：
# 1. 检测到代码变更
# 2. 运行 npm run build
# 3. 部署静态文件
# 4. 更新生产网站
```

#### 手动部署
```bash
# 构建生产版本
npm run build

# 上传 out/ 目录到服务器
# 或使用其他静态网站托管服务
```

## 🔍 质量检查清单

### 创建阶段检查

- [ ] **工具使用**
  - [ ] 使用 `npm run new-article` 创建文章（推荐）
  - [ ] 或正确复制和修改模板文件
  - [ ] 确认文章ID唯一性
  - [ ] 验证文件命名规范

- [ ] **元数据检查**
  - [ ] Front matter 格式正确
  - [ ] 所有必填字段已填写
  - [ ] 分类和标签准确
  - [ ] 日期格式正确

### 内容质量检查

- [ ] **内容完整性**
  - [ ] 文章内容准确、完整
  - [ ] 代码示例可运行
  - [ ] 链接有效
  - [ ] 图片正常显示（如有）

- [ ] **格式规范**
  - [ ] 标题层次清晰（H1-H3）
  - [ ] 代码块指定语言
  - [ ] 表格格式正确
  - [ ] 列表格式统一

- [ ] **SEO 优化**
  - [ ] 标题包含关键词
  - [ ] 摘要描述准确（150-160字符）
  - [ ] 标签相关性强
  - [ ] 内容结构化

### 技术验证检查

- [ ] **本地测试**
  - [ ] `npm run build` 构建成功
  - [ ] 文章页面正常显示
  - [ ] 分类和标签页面更新
  - [ ] 搜索功能正常

- [ ] **功能测试**
  - [ ] 文章在首页显示
  - [ ] 相关文章推荐正确
  - [ ] 面包屑导航正确
  - [ ] 社交分享链接正常

### 发布前最终检查

- [ ] **代码质量**
  - [ ] 无构建错误或警告
  - [ ] 无控制台错误
  - [ ] 页面加载速度正常

- [ ] **内容审核**
  - [ ] 拼写和语法检查
  - [ ] 技术内容准确性
  - [ ] 示例代码测试通过

## 🎯 内容策略建议

### 热门主题方向

1. **AI 技术应用**
   - OpenAI API 使用技巧
   - ChatGPT 集成方案
   - AI 工具评测

2. **现代开发技术**
   - 容器化部署
   - 云原生架构
   - DevOps 实践

3. **实用工具指南**
   - 开发工具配置
   - 浏览器扩展使用
   - 效率提升技巧

### 文章类型建议

- **入门教程**：面向初学者的基础指南
- **进阶实践**：深入的技术实现和优化
- **问题解决**：常见问题的排查和解决
- **工具评测**：新技术和工具的评估
- **最佳实践**：经验总结和建议

## 🛠️ 常见问题解决

### 自动化工具问题

**问题**：`npm run new-article` 命令不存在
**解决**：
```bash
# 确保 package.json 中包含该脚本
# 如果没有，手动添加：
"scripts": {
  "new-article": "node scripts/create-new-article.js"
}
```

**问题**：工具创建的文章ID重复
**解决**：
```bash
# 检查是否有多个相同ID的文章
grep -r "article_id:" data/articles/ | grep "ID号"

# 手动修改重复的ID为下一个可用数字
```

**问题**：工具生成的文件名不符合预期
**解决**：
```bash
# 工具会自动处理特殊字符和空格
# 如需自定义文件名，可以在生成后手动重命名
# 记得同时更新文章内的引用
```

### 构建错误

**问题**：`npm run build` 失败
**解决步骤**：
```bash
# 1. 检查文章 front matter 格式
# 确保 YAML 语法正确，缩进使用空格而非制表符

# 2. 确保 article_id 唯一
grep -r "article_id:" data/articles/ | sort -n

# 3. 验证必填字段
# title, date, excerpt, category, tags, article_id 都必须填写

# 4. 检查特殊字符
# 避免在 YAML 中使用未转义的特殊字符
```

**问题**：文章不显示在分类页面
**检查项**：
```bash
# 1. category 字段是否正确
# 确保使用现有分类：教學, 用戶指南, 站務公告

# 2. category_slug 是否匹配
# 教學 → tutorials
# 用戶指南 → user-guide-cat
# 站務公告 → site-announcements

# 3. 重新生成文章数据
npm run generate-articles-data

# 4. 完整重新构建
npm run build
```

### 格式问题

**问题**：代码块不高亮
```markdown
# 错误写法
```
const code = "example";
```

# 正确写法
```javascript
const code = "example";
```
```

**问题**：表格显示异常
```markdown
# 确保表格格式正确，每行都有相同数量的列
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 值1 | 值2 | 值3 |
```

## 📊 文章数据管理

### 自动生成的文件

构建过程会自动生成以下文件：
- `public/articles-data.json` - 文章索引数据
- `public/rss.xml` - RSS 订阅源
- `public/sitemap.xml` - 搜索引擎站点地图

### 手动更新数据

```bash
# 只更新文章数据
node scripts/generate-articles-data.js

# 只更新 RSS
node scripts/generate-rss.js

# 只更新站点地图
node scripts/generate-sitemap.js
```

## 🎨 高级格式化技巧

### 信息框样式

```markdown
> **💡 小贴士**：这是一个有用的提示信息。

> **⚠️ 注意**：这是需要注意的重要信息。

> **🚨 警告**：这是警告信息，请谨慎操作。

> **✅ 成功**：这表示操作成功完成。
```

### 步骤指南格式

```markdown
### 📋 操作步骤

1. **准备环境**
   ```bash
   npm install
   ```

2. **配置文件**
   创建 `.env` 文件：
   ```bash
   DATABASE_URL=your_database_url
   ```

3. **启动服务**
   ```bash
   npm run dev
   ```

4. **验证结果**
   访问 `http://localhost:3000` 确认服务正常运行。
```

### 对比表格

```markdown
| 特性 | 方案A | 方案B | 推荐 |
|------|-------|-------|------|
| 性能 | 高 | 中 | 方案A |
| 易用性 | 中 | 高 | 方案B |
| 成本 | 低 | 高 | 方案A |
```

## 📈 SEO 优化建议

### 标题优化
- 包含主要关键词
- 长度控制在 50-60 字符
- 使用数字和动词增加吸引力

### 摘要编写
- 控制在 150-160 字符
- 包含文章核心价值
- 使用行动导向的语言

### 内容结构
- 使用 H2、H3 标题组织内容
- 每段控制在 3-4 句话
- 适当使用列表和表格

## 🔄 版本控制最佳实践

### 提交信息格式

```bash
# 新增文章
git commit -m "新增文章：Docker Compose 现代化部署指南"

# 更新文章
git commit -m "更新文章：修复 ChatGPT API 示例代码"

# 修复问题
git commit -m "修复：文章分类显示问题"
```

### 分支管理

```bash
# 创建文章分支
git checkout -b article/docker-compose-guide

# 完成后合并到主分支
git checkout main
git merge article/docker-compose-guide
```

## 📞 获取帮助

### 技术支持

如果在创作过程中遇到问题：

1. **构建错误**：
   - 检查终端错误信息
   - 验证文章格式是否正确
   - 确认所有必填字段已填写

2. **显示问题**：
   - 清除浏览器缓存
   - 重新构建项目
   - 检查文章路径是否正确

3. **内容建议**：
   - 参考现有热门文章
   - 关注技术社区趋势
   - 收集读者反馈

### 联系方式

- **技术问题**：查看项目 GitHub Issues
- **内容建议**：通过网站联系表单反馈
- **紧急问题**：联系项目维护者

---

## 📚 附录：完整示例

### 示例文章：`example-tutorial.md`

```markdown
---
title: Next.js 14 App Router 完整指南
date: 2025-07-01T00:00:00.000Z
excerpt: 深入学习 Next.js 14 的 App Router 特性，包含路由配置、数据获取和性能优化的完整实践指南。
category: 教學
tags:
  - Next.js
  - React
  - Web开发
  - 前端框架
article_id: 9
author: Anmody
readTime: 12 分鐘閱讀
tags_slug:
  - nextjs
  - react
  - web-development
  - frontend-framework
category_slug: tutorials
---

# Next.js 14 App Router 完整指南

Next.js 14 引入了革命性的 App Router，为 React 应用开发带来了全新的体验。本文将深入探讨 App Router 的核心特性和最佳实践。

## 🎯 学习目标

通过本文，您将学会：
- App Router 的基本概念和优势
- 如何配置和使用新的路由系统
- 数据获取的最佳实践
- 性能优化技巧

## 📋 前置需求

- React 基础知识
- Node.js 18+ 环境
- 对 Next.js 基本概念的了解

## 🚀 App Router 简介

### 什么是 App Router？

App Router 是 Next.js 13+ 引入的新路由系统，基于 React Server Components 构建...

[文章内容继续...]

## 💡 最佳实践

1. **合理使用 Server Components**
2. **优化数据获取策略**
3. **实施适当的缓存策略**

## 🔧 故障排除

### 常见问题

**问题**：路由不生效
**解决**：检查文件命名是否符合 App Router 规范...

## 📚 总结

App Router 为 Next.js 应用带来了更好的性能和开发体验...

## 🔗 相关资源

- [Next.js 官方文档](https://nextjs.org/docs)
- [React Server Components](https://react.dev/blog/2023/03/22/react-labs-what-we-have-been-working-on-march-2023)
```

---

**祝您创作愉快！** 🎉

*最后更新：2025年7月1日*
