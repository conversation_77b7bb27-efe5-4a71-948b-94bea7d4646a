import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white mt-auto">
      <div className="container mx-auto px-4 py-12 max-w-7xl">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <div className="flex flex-col">
                <span className="font-bold text-white text-lg">Anmody AI</span>
                <span className="text-xs text-gray-400 -mt-1">教學部落</span>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學，助您掌握最新 AI 應用技術。
            </p>
          </div>

          {/* Navigation */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">導航</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  首頁
                </Link>
              </li>
              <li>
                <Link href="/articles" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  所有文章
                </Link>
              </li>
              <li>
                <Link href="/categories" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  文章分類
                </Link>
              </li>
              <li>
                <Link href="/tags" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  標籤
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  關於我們
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">服務</h4>
            <ul className="space-y-2">
              <li>
                <Link href="https://t.afffun.com/cmvp88bt" className="text-gray-300 hover:text-blue-400 text-sm transition-colors" target="_blank" rel="noopener noreferrer">
                  API Key 購買
                </Link>
              </li>
              <li>
                <Link href="https://www.anmody.com" className="text-gray-300 hover:text-blue-400 text-sm transition-colors" target="_blank" rel="noopener noreferrer">
                  ChatGPT Plus 訂閱
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  技術諮詢
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  業務合作
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-4">法律資訊</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/privacy" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  隱私政策
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  使用條款
                </Link>
              </li>
              <li>
                <Link href="/sitemap.xml" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  網站地圖
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-blue-400 text-sm transition-colors">
                  聯絡我們
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 Anmody AI 教學部落. 版權所有。
            </p>
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">
                Made with ❤️ for AI learners
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}