#!/usr/bin/env node

// 标签 URL 测试脚本
// 验证所有标签页面是否正确生成

const fs = require('fs');
const path = require('path');

const outDir = path.join(__dirname, '..', 'out');
const tagsDir = path.join(outDir, 'tags');

// 标签映射 - 与 lib/tags.ts 保持一致
const tagMapping = {
  '快速入門': 'quick-start',
  '使用指南': 'user-guide',
  '安裝指南': 'installation-guide',
  '對話管理': 'conversation-management',
  '站務': 'site-management',
  '開發者模式': 'developer-mode',
  '關於': 'about',
  'Chrome 擴展': 'chrome-extension',
  'ZIP 安裝': 'zip-installation',
};

function testTagUrls() {
  console.log('🔍 检查标签目录结构...\n');
  
  if (!fs.existsSync(tagsDir)) {
    console.error('❌ 标签目录不存在:', tagsDir);
    return false;
  }

  const tagDirs = fs.readdirSync(tagsDir).filter(item => {
    const itemPath = path.join(tagsDir, item);
    return fs.statSync(itemPath).isDirectory();
  });

  console.log('📁 发现标签目录:');
  tagDirs.forEach(dir => {
    console.log(`  - ${dir}`);
  });
  console.log('');

  // 检查 slug 化的标签是否存在
  console.log('✅ 检查 slug 化标签:');
  let slugSuccess = 0;
  let slugTotal = 0;
  
  Object.entries(tagMapping).forEach(([original, slug]) => {
    slugTotal++;
    const slugDir = path.join(tagsDir, slug);
    const indexFile = path.join(slugDir, 'index.html');
    
    if (fs.existsSync(indexFile)) {
      console.log(`  ✅ ${slug} (${original})`);
      slugSuccess++;
    } else {
      console.log(`  ❌ ${slug} (${original}) - 缺少文件`);
    }
  });

  console.log(`\n📊 Slug 化标签: ${slugSuccess}/${slugTotal} 成功\n`);

  // 检查编码版本是否存在（用于兼容性）
  console.log('🔄 检查编码版本标签:');
  let encodedSuccess = 0;
  let encodedTotal = 0;
  
  Object.keys(tagMapping).forEach(original => {
    encodedTotal++;
    const encoded = encodeURIComponent(original);
    const encodedDir = path.join(tagsDir, encoded);
    const indexFile = path.join(encodedDir, 'index.html');
    
    if (fs.existsSync(indexFile)) {
      console.log(`  ✅ ${encoded} (${original})`);
      encodedSuccess++;
    } else {
      console.log(`  ⚠️  ${encoded} (${original}) - 无编码版本`);
    }
  });

  console.log(`\n📊 编码版本标签: ${encodedSuccess}/${encodedTotal} 存在\n`);

  // 检查其他标签
  const mappedTags = new Set([
    ...Object.keys(tagMapping),
    ...Object.values(tagMapping),
    ...Object.keys(tagMapping).map(tag => encodeURIComponent(tag))
  ]);

  const otherTags = tagDirs.filter(dir => !mappedTags.has(dir) && !mappedTags.has(decodeURIComponent(dir)));
  
  if (otherTags.length > 0) {
    console.log('📋 其他标签目录:');
    otherTags.forEach(dir => {
      try {
        const decoded = decodeURIComponent(dir);
        console.log(`  - ${dir}${decoded !== dir ? ` (${decoded})` : ''}`);
      } catch {
        console.log(`  - ${dir}`);
      }
    });
    console.log('');
  }

  const allSuccess = slugSuccess === slugTotal;
  console.log(allSuccess ? '🎉 所有测试通过！' : '⚠️  存在问题，请检查');
  
  return allSuccess;
}

// 运行测试
if (require.main === module) {
  testTagUrls();
}
