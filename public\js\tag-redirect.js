// Chinese tag URL redirect handler
// This script handles client-side redirects for Chinese tag URLs

(function() {
  // Tag mapping for Chinese to English slug conversion
  const tagMapping = {
    '快速入門': 'quick-start',
    '使用指南': 'user-guide',
    '安裝指南': 'installation-guide',
    '對話管理': 'conversation-management',
    '站務': 'site-management',
    '開發者模式': 'developer-mode',
    '關於': 'about',
    'Chrome 擴展': 'chrome-extension',
    'ZIP 安裝': 'zip-installation'
  };

  // URL encoded mapping
  const encodedMapping = {
    '%E5%BF%AB%E9%80%9F%E5%85%A5%E9%96%80': 'quick-start',
    '%E4%BD%BF%E7%94%A8%E6%8C%87%E5%8D%97': 'user-guide',
    '%E5%AE%89%E8%A3%9D%E6%8C%87%E5%8D%97': 'installation-guide',
    '%E5%B0%8D%E8%A9%B1%E7%AE%A1%E7%90%86': 'conversation-management',
    '%E7%AB%99%E5%8B%99': 'site-management',
    '%E9%96%8B%E7%99%BC%E8%80%85%E6%A8%A1%E5%BC%8F': 'developer-mode',
    '%E9%97%9C%E6%96%BC': 'about',
    'Chrome%20%E6%93%B4%E5%B1%95': 'chrome-extension',
    'ZIP%20%E5%AE%89%E8%A3%9D': 'zip-installation'
  };

  function handleTagRedirect() {
    const currentPath = window.location.pathname;
    
    // Check if we're on a tag page
    if (currentPath.startsWith('/tags/')) {
      const tagPart = currentPath.substring('/tags/'.length);
      const tagParam = tagPart.split('/')[0];
      
      // Check for direct mapping
      if (encodedMapping[tagParam]) {
        const newPath = `/tags/${encodedMapping[tagParam]}`;
        if (newPath !== currentPath) {
          window.location.replace(newPath);
          return;
        }
      }
      
      // Try to decode and check mapping
      try {
        const decodedTag = decodeURIComponent(tagParam);
        if (tagMapping[decodedTag]) {
          const newPath = `/tags/${tagMapping[decodedTag]}`;
          if (newPath !== currentPath) {
            window.location.replace(newPath);
            return;
          }
        }
      } catch (e) {
        // Ignore decode errors
      }
    }
  }

  // Run on page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', handleTagRedirect);
  } else {
    handleTagRedirect();
  }
})();
