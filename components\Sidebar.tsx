"use client";
import Link from 'next/link'
import { Article } from '@/lib/articles'
import { slugifyTag, slugifyCategory } from '@/lib/tags'

interface SidebarProps {
  articles: Article[]
}

export default function Sidebar({ articles }: SidebarProps) {
  const recentArticles = articles.slice(0, 5)
  
  // 獲取所有分類
  const categories = Array.from(
    new Set(articles.map(article => article.category).filter(Boolean))
  ) as string[]
  
  // 獲取所有標籤
  const allTags = articles.flatMap(article => article.tags || [])
  const tagCounts = allTags.reduce((acc, tag) => {
    acc[tag] = (acc[tag] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const popularTags = Object.entries(tagCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)

  return (
    <aside className="space-y-8">
      {/* About Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">關於本站</h3>
        <p className="text-gray-600 text-sm leading-relaxed mb-4">
          Anmody AI 教學部落專注於提供高質量的人工智慧技術教學內容，包括 OpenAI API、ChatGPT 應用開發等前沿技術。
        </p>
        <Link 
          href="/about" 
          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
        >
          了解更多 →
        </Link>
      </div>

      {/* Recent Articles */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">最新文章</h3>
        <div className="space-y-3">
          {recentArticles.map((article) => (
            <Link
              key={article.slug}
              href={`/articles/${article.slug}`}
              className="block group"
            >
              <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 line-clamp-2 mb-1">
                {article.title}
              </h4>
              <time className="text-xs text-gray-500">
                {article.date instanceof Date ? 
                  article.date.toLocaleDateString('zh-HK') : 
                  article.date
                }
              </time>
            </Link>
          ))}
        </div>
      </div>

      {/* Categories */}
      {categories.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">文章分類</h3>
          <div className="space-y-2">
            {categories.map((category) => {
              const count = articles.filter(article => article.category === category).length
              return (
                <Link
                  key={category}
                  href={`/categories/${slugifyCategory(category)}`}
                  className="flex justify-between items-center text-sm text-gray-600 hover:text-blue-600 py-1"
                >
                  <span>{category}</span>
                  <span className="bg-gray-100 text-gray-500 px-2 py-0.5 rounded-full text-xs">
                    {count}
                  </span>
                </Link>
              )
            })}
          </div>
        </div>
      )}

      {/* Popular Tags */}
      {popularTags.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">熱門標籤</h3>
          <div className="flex flex-wrap gap-2">
            {popularTags.map(([tag, count]) => (
              <Link
                key={tag}
                href={`/tags/${slugifyTag(tag)}`}
                className="inline-block bg-gray-100 hover:bg-blue-100 text-gray-700 hover:text-blue-700 px-3 py-1 rounded-full text-sm transition-colors"
              >
                #{tag} ({count})
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Newsletter Signup */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h3 className="text-lg font-semibold mb-2">訂閱更新</h3>
        <p className="text-blue-100 text-sm mb-4">
          獲取最新的 AI 技術文章和教學資源
        </p>
        <Link 
          href="/contact" 
          className="inline-block bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-50 transition-colors"
        >
          聯絡我們
        </Link>
      </div>

      {/* Archive */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">文章歸檔</h3>
        <div className="space-y-2">
          <Link 
            href="/archive" 
            className="block text-sm text-gray-600 hover:text-blue-600"
          >
            查看所有文章
          </Link>
          <Link 
            href="/sitemap.xml" 
            className="block text-sm text-gray-600 hover:text-blue-600"
          >
            網站地圖
          </Link>
        </div>
      </div>
    </aside>
  )
}
