import { redirect } from 'next/navigation'
import { getAllArticles } from '@/lib/articles'

export async function generateStaticParams() {
  const articles = getAllArticles()
  return articles.map((article) => ({
    slug: article.slug,
  }))
}

// Redirect old announcement URLs to new article URLs
export default async function AnnouncementRedirect({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  redirect(`/articles/${slug}`)
}