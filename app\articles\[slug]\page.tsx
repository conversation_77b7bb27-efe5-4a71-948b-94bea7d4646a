import { notFound } from 'next/navigation'
import { getAllArticles, getArticleContent, getRelatedArticles } from '@/lib/articles'
import { slugifyTag, slugifyCategory } from '@/lib/tags'
import MarkdownContent from '@/components/MarkdownContent'
import ArticleCard from '@/components/ArticleCard'
import Link from 'next/link'
import type { Metadata } from 'next'
import Script from 'next/script'

export async function generateStaticParams() {
  const articles = getAllArticles()
  return articles.map((article) => ({
    slug: article.slug,
  }))
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params

  try {
    const article = getArticleContent(slug)

    return {
      title: `${article.title} | Anmody AI 教學部落`,
      description: article.excerpt,
      keywords: article.tags?.join(', '),
      authors: [{ name: article.author || 'Anmo<PERSON>' }],
      openGraph: {
        title: article.title,
        description: article.excerpt,
        type: 'article',
        publishedTime: article.date instanceof Date ? article.date.toISOString() : article.date,
        authors: [article.author || 'Anmody'],
        tags: article.tags,
        url: `https://learnmarts.com/articles/${slug}`,
      },
      twitter: {
        card: 'summary_large_image',
        title: article.title,
        description: article.excerpt,
      },
      alternates: {
        canonical: `https://learnmarts.com/articles/${slug}`,
      },
    }
  } catch (error) {
    return {
      title: '文章未找到 | Anmody AI 教學部落',
      description: '抱歉，您要查找的文章不存在。',
    }
  }
}

export default async function ArticlePage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params

  let article
  try {
    article = getArticleContent(slug)
  } catch (error) {
    notFound()
  }

  const relatedArticles = getRelatedArticles(slug, article.category, article.tags)

  // 結構化資料
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: article.title,
    description: article.excerpt,
    author: {
      '@type': 'Organization',
      name: article.author || 'Anmody'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Anmody',
      logo: {
        '@type': 'ImageObject',
        url: 'https://learnmarts.com/images/logo.png'
      }
    },
    datePublished: article.date instanceof Date ? article.date.toISOString() : article.date,
    dateModified: article.date instanceof Date ? article.date.toISOString() : article.date,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://learnmarts.com/articles/${slug}`
    },
    articleSection: article.category,
    keywords: article.tags?.join(', '),
    inLanguage: 'zh-Hant-HK'
  }

  return (
    <>
      <Script
        id="article-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <article className="max-w-4xl mx-auto">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
          <Link href="/" className="hover:text-blue-600">首頁</Link>
          <span>/</span>
          <Link href="/articles" className="hover:text-blue-600">文章</Link>
          {article.category && (
            <>
              <span>/</span>
              <Link
                href={`/categories/${slugifyCategory(article.category)}`}
                className="hover:text-blue-600"
              >
                {article.category}
              </Link>
            </>
          )}
          <span>/</span>
          <span className="text-gray-900">{article.title}</span>
        </nav>

        {/* Article Header */}
        <header className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {article.title}
          </h1>

          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <time>
                {article.date instanceof Date ?
                  article.date.toLocaleDateString('zh-HK') :
                  article.date
                }
              </time>
            </div>

            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>{article.author}</span>
            </div>

            {article.readTime && (
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{article.readTime}</span>
              </div>
            )}
          </div>

          {/* Category and Tags */}
          <div className="flex flex-wrap gap-4 mb-6">
            {article.category && (
              <Link
                href={`/categories/${slugifyCategory(article.category)}`}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                {article.category}
              </Link>
            )}

            {article.tags && article.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {article.tags.map((tag) => (
                  <Link
                    key={tag}
                    href={`/tags/${slugifyTag(tag)}`}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                  >
                    #{tag}
                  </Link>
                ))}
              </div>
            )}
          </div>

          {/* Article Excerpt */}
          {article.excerpt && (
            <div className="text-lg text-gray-600 leading-relaxed p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
              {article.excerpt}
            </div>
          )}
        </header>

        {/* Article Content */}
        <div className="prose prose-lg prose-slate max-w-none mb-12">
          <MarkdownContent content={article.content || ''} />
        </div>

        {/* Article Footer */}
        <footer className="border-t border-gray-200 pt-8">
          {/* Share Buttons */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700">分享文章：</span>
              <div className="flex gap-2">
                <a
                  href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(article.title)}&url=${encodeURIComponent(`https://learnmarts.com/articles/${slug}`)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 text-gray-500 hover:text-blue-500 transition-colors"
                  aria-label="分享到 Twitter"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
                <a
                  href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(`https://learnmarts.com/articles/${slug}`)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 text-gray-500 hover:text-blue-600 transition-colors"
                  aria-label="分享到 Facebook"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
              </div>
            </div>

            <Link
              href="/articles"
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
            >
              ← 返回文章列表
            </Link>
          </div>
        </footer>
      </article>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <section className="max-w-6xl mx-auto mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">相關文章</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedArticles.map((relatedArticle, index) => (
              <div
                key={relatedArticle.slug}
                className={`transform hover:-translate-y-1 transition-all duration-300 article-item delay-${index}`}
              >
                <ArticleCard {...relatedArticle} />
              </div>
            ))}
          </div>
        </section>
      )}
    </>
  )
}