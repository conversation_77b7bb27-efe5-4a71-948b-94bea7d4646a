import { getAllArticles } from '@/lib/articles'
import Link from 'next/link'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '文章歸檔 | Anmody AI 教學部落',
  description: '按時間順序瀏覽所有 AI 技術教學文章，快速找到您需要的內容。',
}

export default function ArchivePage() {
  const articles = getAllArticles()
  
  // 按年份分組文章
  const articlesByYear = articles.reduce((acc, article) => {
    const year = new Date(article.date).getFullYear()
    if (!acc[year]) {
      acc[year] = []
    }
    acc[year].push(article)
    return acc
  }, {} as Record<number, typeof articles>)

  const years = Object.keys(articlesByYear)
    .map(Number)
    .sort((a, b) => b - a)

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">文章歸檔</h1>
        <p className="text-xl text-gray-600">
          按時間順序瀏覽所有文章，共 {articles.length} 篇
        </p>
      </div>

      {years.length > 0 ? (
        <div className="space-y-12">
          {years.map(year => (
            <section key={year} className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span className="w-1 h-6 bg-blue-600 mr-3"></span>
                {year} 年
                <span className="ml-3 text-sm font-normal text-gray-500">
                  ({articlesByYear[year].length} 篇文章)
                </span>
              </h2>
              
              <div className="space-y-4">
                {articlesByYear[year].map(article => {
                  const formattedDate = article.date instanceof Date 
                    ? article.date.toLocaleDateString('zh-HK')
                    : article.date
                  
                  return (
                    <div key={article.slug} className="flex flex-col md:flex-row md:items-center gap-4 p-4 hover:bg-gray-50 rounded-lg transition-colors">
                      <div className="md:w-24 flex-shrink-0">
                        <time className="text-sm text-gray-500 font-mono">
                          {formattedDate}
                        </time>
                      </div>
                      
                      <div className="flex-grow">
                        <Link 
                          href={`/articles/${article.slug}`}
                          className="block group"
                        >
                          <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600 transition-colors mb-1">
                            {article.title}
                          </h3>
                          <p className="text-gray-600 text-sm line-clamp-2">
                            {article.excerpt}
                          </p>
                        </Link>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 md:w-48 flex-shrink-0">
                        {article.category && (
                          <Link
                            href={`/categories/${encodeURIComponent(article.category)}`}
                            className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full hover:bg-blue-200 transition-colors"
                          >
                            {article.category}
                          </Link>
                        )}
                        {article.tags && article.tags.slice(0, 2).map(tag => (
                          <Link
                            key={tag}
                            href={`/tags/${encodeURIComponent(tag)}`}
                            className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full hover:bg-gray-200 transition-colors"
                          >
                            #{tag}
                          </Link>
                        ))}
                      </div>
                    </div>
                  )
                })}
              </div>
            </section>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">暫無文章</p>
        </div>
      )}

      {/* Back to top */}
      <div className="text-center mt-12">
        <a
          href="#top"
          className="inline-block bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
        >
          回到頂部
        </a>
      </div>
    </div>
  )
}
