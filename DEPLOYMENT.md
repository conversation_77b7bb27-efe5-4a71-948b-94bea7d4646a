# 部署指南

## 搜索功能修复完成 ✅

### 已完成的修复：

1. **搜索功能重构**：
   - 从服务器端 API 改为客户端搜索
   - 使用静态 JSON 文件存储文章数据
   - 添加 Suspense 边界处理 useSearchParams

2. **静态导出兼容性**：
   - 移除了不兼容的 API 路由
   - 确保所有功能都支持静态导出
   - 生成了 `articles-data.json` 用于客户端搜索

3. **构建验证**：
   - ✅ 编译成功
   - ✅ 类型检查通过
   - ✅ 静态页面生成成功 (61/61)
   - ✅ 搜索页面正常构建

## Cloudflare Pages 部署准备

### 当前配置状态：

#### `next.config.js` ✅
```javascript
const nextConfig = {
  output: 'export',
  images: {
    unoptimized: true
  },
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': __dirname,
    }
    return config
  }
}
```

#### `wrangler.toml` ✅
```toml
name = "teaching-anmody-com"
compatibility_date = "2024-01-25"
pages_build_output_dir = "out"
```

#### `package.json` 构建脚本 ✅
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "node scripts/generate-sitemap.js && npx next build",
    "start": "next start",
    "generate-sitemap": "node scripts/generate-sitemap.js"
  }
}
```

## 部署到 Cloudflare Pages

### 方法 1: 通过 Git 连接（推荐）

1. **推送代码到 Git 仓库**：
   ```bash
   git add .
   git commit -m "feat: 完成搜索功能修复，准备部署到 Cloudflare Pages"
   git push origin main
   ```

2. **在 Cloudflare Pages 创建项目**：
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 进入 Pages 部分
   - 点击 "Create a project"
   - 选择 "Connect to Git"
   - 选择你的 Git 仓库

3. **配置构建设置**：
   - **Framework preset**: Next.js (Static HTML Export)
   - **Build command**: `npm run build`
   - **Build output directory**: `out`
   - **Root directory**: `/` (保持默认)

4. **环境变量**（如果需要）：
   - 目前项目不需要特殊环境变量

### 方法 2: 直接上传（快速测试）

1. **本地构建**：
   ```bash
   npm run build
   ```

2. **上传 out 目录**：
   - 进入 Cloudflare Pages
   - 选择 "Upload assets"
   - 拖拽 `out` 文件夹上传

## 验证部署

部署完成后，验证以下功能：

### ✅ 核心功能检查清单：

- [ ] 首页加载正常
- [ ] 文章列表显示
- [ ] 单篇文章页面
- [ ] 搜索功能工作
- [ ] 分类和标签页面
- [ ] 响应式设计
- [ ] 图片和资源加载

### 🔍 搜索功能测试：

1. 访问 `/search` 页面
2. 测试搜索关键词：
   - "ChatGPT"
   - "教學"
   - "API"
3. 验证搜索结果正确显示
4. 测试 URL 参数 `/search?q=keyword`

## 性能优化

当前构建产物大小：
- 搜索页面: 3.4 kB + 99.3 kB (First Load)
- 首页: 1.9 kB + 100 kB (First Load)
- 文章页面: 84 kB + 182 kB (First Load)

### 已实现的优化：

1. **静态导出** - 零服务器运行时
2. **图片优化禁用** - 兼容静态托管
3. **客户端搜索** - 快速响应，无需 API 请求
4. **代码分割** - Next.js 自动优化

## 自定义域名配置

部署成功后，在 Cloudflare Pages 项目设置中：

1. 进入 "Custom domains" 选项卡
2. 添加你的域名（如 `learnmarts.com`）
3. 配置 DNS 记录指向 Cloudflare Pages

## 故障排除

### 常见问题：

1. **构建失败** - 检查 Node.js 版本 (需要 >=18.18.0)
2. **搜索不工作** - 确保 `articles-data.json` 文件存在于 public 目录
3. **样式问题** - 验证 Tailwind CSS 配置正确

### 日志检查：

在 Cloudflare Pages 项目的 "Functions" 选项卡中查看构建日志。

---

🎉 **恭喜！** 你的 Next.js 博客网站现在已经完全准备好部署到 Cloudflare Pages 了！

搜索功能已经完全修复并优化为静态导出兼容模式。整个网站现在可以作为静态资源部署，提供快速的加载速度和优秀的用户体验。
