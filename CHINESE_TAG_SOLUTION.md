# 中文标签 URL 处理方案

## 问题描述
在 Cloudflare Pages 等静态托管环境下，中文标签页面（如 `/tags/快速入門`）会出现 404 错误，因为 URL 编码处理存在兼容性问题。

## 解决方案

### 1. 标签 Slug 化
- 为中文标签创建英文 slug 别名
- 例如：`快速入門` → `quick-start`
- 保持原有中文标签的完整性，仅在 URL 中使用 slug

### 2. 多层重定向处理

#### 2.1 服务器端重定向 (`public/_redirects`)
```
# 将旧的编码 URL 重定向到新的 slug URL
/tags/%E5%BF%AB%E9%80%9F%E5%85%A5%E9%96%80 /tags/quick-start 301
/tags/快速入門 /tags/quick-start 301
```

#### 2.2 客户端重定向 (`public/js/tag-redirect.js`)
- 处理可能遗漏的 URL 重定向
- 在页面加载时检查并重定向不正确的 URL

#### 2.3 静态生成 (`generateStaticParams`)
- 同时生成 slug 版本和编码版本的静态页面
- 确保所有可能的 URL 都有对应的静态文件

### 3. SEO 优化
- Sitemap 使用 slug 化的 URL
- Meta 标签和 OpenGraph 使用原始中文标签名称
- Canonical URL 指向 slug 版本

## 实施步骤

### 1. 创建标签映射 (`lib/tags.ts`)
```typescript
export function slugifyTag(tag: string): string {
  const tagMapping = {
    '快速入門': 'quick-start',
    '使用指南': 'user-guide',
    // ... 更多映射
  }
  return tagMapping[tag] || tag
}
```

### 2. 更新标签页面 (`app/tags/[tag]/page.tsx`)
- 使用 `getTagSlugAndOriginal` 处理参数
- 生成双版本静态参数

### 3. 更新组件链接
- `app/tags/page.tsx` - 标签列表页面
- `components/ArticleCard.tsx` - 文章卡片中的标签链接

### 4. 配置重定向文件
- `public/_redirects` - Cloudflare Pages 重定向规则
- `public/js/tag-redirect.js` - 客户端重定向脚本

### 5. 更新构建脚本
- `scripts/generate-sitemap.js` - 使用 slug 化的 URL

## 文件清单

### 新增文件
- `lib/tags.ts` - 标签处理工具
- `public/_redirects` - Cloudflare Pages 重定向配置
- `public/js/tag-redirect.js` - 客户端重定向脚本
- `wrangler.json` - Cloudflare Pages 路由配置

### 修改文件
- `app/tags/[tag]/page.tsx` - 标签页面
- `app/tags/page.tsx` - 标签列表页面
- `components/ArticleCard.tsx` - 文章卡片组件
- `app/layout.tsx` - 添加重定向脚本
- `scripts/generate-sitemap.js` - 使用 slug URL
- `wrangler.toml` - 更新项目名称

## 测试验证

### 本地测试
1. `npm run build` - 构建项目
2. 检查 `out/tags/` 目录结构
3. 验证 `public/sitemap.xml` 中的 URL

### 部署后测试
1. 访问 slug 化的 URL：`https://learnmarts.com/tags/quick-start`
2. 验证中文 URL 重定向：`https://learnmarts.com/tags/快速入門`
3. 检查编码 URL 重定向：`https://learnmarts.com/tags/%E5%BF%AB%E9%80%9F%E5%85%A5%E9%96%80`

## 部署说明

### Cloudflare Pages
1. 确保 `_redirects` 文件在构建输出目录中
2. 验证 `wrangler.toml` 配置正确
3. 确认所有静态文件都已生成

### 其他静态托管
- Vercel：支持 `_redirects` 文件
- Netlify：支持 `_redirects` 文件
- GitHub Pages：需要使用 Jekyll 重定向插件

## 注意事项

1. **保持映射一致性**：确保 `lib/tags.ts` 和 `scripts/generate-sitemap.js` 中的映射一致
2. **添加新标签**：新增中文标签时，需要在映射中添加对应的 slug
3. **重定向优先级**：服务器端重定向优先于客户端重定向
4. **SEO 友好**：使用 301 重定向确保 SEO 权重传递

## 维护建议

1. 定期检查标签映射的完整性
2. 监控 404 错误日志，发现遗漏的重定向规则
3. 考虑实施自动化测试来验证所有标签 URL 的可访问性
