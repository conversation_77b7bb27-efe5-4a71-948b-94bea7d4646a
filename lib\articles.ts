import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

const articlesDirectory = path.join(process.cwd(), 'data/articles')

export interface Article {
  slug: string
  title: string
  excerpt: string
  date: string | Date
  category?: string
  tags?: string[]
  article_id?: number
  content?: string
  author?: string
  readTime?: string
}

export function getArticleContent(slug: string): Article {
  const fullPath = path.join(articlesDirectory, `${slug}.md`)
  const fileContents = fs.readFileSync(fullPath, 'utf8')
  const { data, content } = matter(fileContents)

  // Calculate read time (rough estimate: 200 words per minute)
  const wordCount = content.split(/\s+/).length
  const readTime = Math.ceil(wordCount / 200)

  return {
    slug,
    title: data.title,
    date: data.date,
    content: content,
    excerpt: data.excerpt,
    category: data.category,
    tags: data.tags,
    article_id: data.article_id,
    author: data.author || 'Anmody',
    readTime: `${readTime} 分鐘閱讀`
  }
}

export function getAllArticles(): Article[] {
  const fileNames = fs.readdirSync(articlesDirectory)

  return fileNames
    .filter(fileName => fileName.endsWith('.md'))
    .map(fileName => {
      const slug = fileName.replace(/\.md$/, '')
      const fullPath = path.join(articlesDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data, content } = matter(fileContents)

      // Calculate read time
      const wordCount = content.split(/\s+/).length
      const readTime = Math.ceil(wordCount / 200)

      return {
        slug,
        title: data.title,
        date: data.date,
        excerpt: data.excerpt,
        category: data.category,
        tags: data.tags,
        article_id: data.article_id,
        author: data.author || 'Anmody',
        readTime: `${readTime} 分鐘閱讀`
      }
    })
    .sort((a, b) => (Number(b.article_id) - Number(a.article_id)))
}

export function getArticlesByCategory(category: string): Article[] {
  return getAllArticles().filter(article => article.category === category)
}

export function getArticlesByTag(tag: string): Article[] {
  return getAllArticles().filter(article =>
    article.tags && article.tags.includes(tag)
  )
}

export function getRelatedArticles(currentSlug: string, category?: string, tags?: string[]): Article[] {
  const allArticles = getAllArticles().filter(article => article.slug !== currentSlug)

  if (!category && !tags) return allArticles.slice(0, 3)

  // Score articles based on category and tag matches
  const scoredArticles = allArticles.map(article => {
    let score = 0

    if (category && article.category === category) {
      score += 3
    }

    if (tags && article.tags) {
      const commonTags = tags.filter(tag => article.tags!.includes(tag))
      score += commonTags.length
    }

    return { ...article, score }
  })

  return scoredArticles
    .sort((a, b) => b.score - a.score)
    .slice(0, 3)
}

