// 用于处理 URL 安全的标签和分类转换，采用自动 slug 化方案，无需手动维护映射

// 改进的 slugify 函数：优先使用完整词组映射，避免字符级别处理
export function slugify(str: string): string {
  // 首先尝试直接映射整个字符串
  const directMapping = chineseToSlug(str);
  if (directMapping !== str) {
    return directMapping;
  }

  // 对于英文或混合内容，进行标准化处理
  return str
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '') // 去除重音符号
    .replace(/\s+/g, '-') // 空格转 -
    .replace(/[_\u00b7\u2022\u2014\u2013]/g, '-') // 常见分隔符转 -
    .replace(/[^\w-]/g, '') // 只保留字母、数字、-
    .replace(/-+/g, '-') // 合并多个连字符
    .replace(/^-|-$/g, '') // 去除首尾连字符
    .toLowerCase();
}

// 中文词组到英文 slug 的映射
function chineseToSlug(text: string): string {
  const chineseSlugMap: { [key: string]: string } = {
    '快速入門': 'quick-start',
    '使用指南': 'user-guide',
    '安裝指南': 'installation-guide',
    '對話管理': 'conversation-management',
    '站務': 'site-management',
    '開發者模式': 'developer-mode',
    '關於': 'about',
    'Chrome 擴展': 'chrome-extension',
    'ZIP 安裝': 'zip-installation',
    '教學': 'tutorials',
    '用戶指南': 'user-guide-cat',
    '站務公告': 'site-announcements',
    'ChatGPT': 'chatgpt',
    'Plus': 'plus',
    'Android': 'android',
    'OpenAI': 'openai',
    'API': 'api'
  };

  // 直接查找完整匹配
  if (chineseSlugMap[text]) {
    return chineseSlugMap[text];
  }

  // 如果没有找到映射，保持原文（用于英文标签）
  return text;
}

// unslugify 仅做 slug 还原（如有需要可做映射，默认返回 slug）
export function unslugify(slug: string): string {
  return slug;
}

// 标签/分类通用 slug 化
export function slugifyTag(tag: string): string {
  return slugify(tag);
}
export function unslugifyTag(slug: string): string {
  return unslugify(slug);
}
export function slugifyCategory(category: string): string {
  return slugify(category);
}
export function unslugifyCategory(slug: string): string {
  return unslugify(slug);
}

// 获取 slug 和原文（兼容 decodeURIComponent）
export function getCategorySlugAndOriginal(categoryParam: string): { slug: string; original: string } {
  let decodedCategory: string;
  try {
    decodedCategory = decodeURIComponent(categoryParam);
  } catch {
    decodedCategory = categoryParam;
  }
  const slugified = slugifyCategory(decodedCategory);
  return { slug: slugified, original: decodedCategory };
}

export function getTagSlugAndOriginal(tagParam: string): { slug: string; original: string } {
  let decodedTag: string;
  try {
    decodedTag = decodeURIComponent(tagParam);
  } catch {
    decodedTag = tagParam;
  }
  const slugified = slugifyTag(decodedTag);
  return { slug: slugified, original: decodedTag };
}