// 用于处理 URL 安全的标签和分类转换，采用自动 slug 化方案，无需手动维护映射

// 简单 slugify：中文转拼音（如需更好效果可引入 pinyin 库），英文/数字保留，小写，空格转 -，去除特殊字符
export function slugify(str: string): string {
  return str
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '') // 去除重音符号
    .replace(/\s+/g, '-') // 空格转 -
    .replace(/[_\u00b7\u2022\u2014\u2013\u2014\u2013]/g, '-') // 常见分隔符转 -
    .replace(/[^\w\u4e00-\u9fa5-]/g, '') // 保留中英文、数字、-
    .replace(/[\u4e00-\u9fa5]+/g, (m) =>
      Array.from(m).map(c => chineseToPinyin(c)).join('-')
    )
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase();
}

// 简单中文转拼音（仅首字母，避免引入大库，适合 SEO slug）
function chineseToPinyin(char: string): string {
  const pinyinMap: { [key: string]: string } = {
    '快速入門': 'quick-start',
    '使用指南': 'user-guide',
    '安裝指南': 'installation-guide',
    '對話管理': 'conversation-management',
    '站務': 'site-management',
    '開發者模式': 'developer-mode',
    '關於': 'about',
    'Chrome 擴展': 'chrome-extension',
    'ZIP 安裝': 'zip-installation'
  };
  return pinyinMap[char] || 'zh';
}

// unslugify 仅做 slug 还原（如有需要可做映射，默认返回 slug）
export function unslugify(slug: string): string {
  return slug;
}

// 标签/分类通用 slug 化
export function slugifyTag(tag: string): string {
  return slugify(tag);
}
export function unslugifyTag(slug: string): string {
  return unslugify(slug);
}
export function slugifyCategory(category: string): string {
  return slugify(category);
}
export function unslugifyCategory(slug: string): string {
  return unslugify(slug);
}

// 获取 slug 和原文（兼容 decodeURIComponent）
export function getCategorySlugAndOriginal(categoryParam: string): { slug: string; original: string } {
  let decodedCategory: string;
  try {
    decodedCategory = decodeURIComponent(categoryParam);
  } catch {
    decodedCategory = categoryParam;
  }
  const slugified = slugifyCategory(decodedCategory);
  return { slug: slugified, original: decodedCategory };
}

export function getTagSlugAndOriginal(tagParam: string): { slug: string; original: string } {
  let decodedTag: string;
  try {
    decodedTag = decodeURIComponent(tagParam);
  } catch {
    decodedTag = tagParam;
  }
  const slugified = slugifyTag(decodedTag);
  return { slug: slugified, original: decodedTag };
}