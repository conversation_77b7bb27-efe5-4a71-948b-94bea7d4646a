// 用于处理 URL 安全的标签和分类转换，采用完全自动化方案，无需手动维护映射

// 完全自动化的 slugify 函数：处理中文、英文、混合内容
export function slugify(str: string): string {
  return str
    .normalize('NFKD') // Unicode 标准化
    .replace(/[\u0300-\u036f]/g, '') // 去除重音符号
    .replace(/\s+/g, '-') // 空格转连字符
    .replace(/[_\u00b7\u2022\u2014\u2013]/g, '-') // 常见分隔符转连字符
    .replace(/[^\w\u4e00-\u9fff-]/g, '') // 保留字母、数字、中文、连字符
    .replace(/-+/g, '-') // 合并多个连字符
    .replace(/^-|-$/g, '') // 去除首尾连字符
    .toLowerCase();
}

// 移除了手动映射表，改用完全自动化处理

// unslugify：将 slug 转换回可读格式
export function unslugify(slug: string): string {
  // 对于包含中文的 slug，直接返回（中文在 URL 中会被编码，但在显示时正常）
  // 对于英文 slug，转换为标题格式
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// 标签/分类通用 slug 化
export function slugifyTag(tag: string): string {
  return slugify(tag);
}
export function unslugifyTag(slug: string): string {
  return unslugify(slug);
}
export function slugifyCategory(category: string): string {
  return slugify(category);
}
export function unslugifyCategory(slug: string): string {
  return unslugify(slug);
}

// 获取 slug 和原文（自动化处理，无需手动映射）
export function getCategorySlugAndOriginal(categoryParam: string): { slug: string; original: string } {
  let decodedCategory: string;
  try {
    decodedCategory = decodeURIComponent(categoryParam);
  } catch {
    decodedCategory = categoryParam;
  }

  // 简化逻辑：直接使用输入作为原文，生成对应的 slug
  const slugified = slugifyCategory(decodedCategory);
  return { slug: slugified, original: decodedCategory };
}

export function getTagSlugAndOriginal(tagParam: string): { slug: string; original: string } {
  let decodedTag: string;
  try {
    decodedTag = decodeURIComponent(tagParam);
  } catch {
    decodedTag = tagParam;
  }

  // 简化逻辑：直接使用输入作为原文，生成对应的 slug
  const slugified = slugifyTag(decodedTag);
  return { slug: slugified, original: decodedTag };
}