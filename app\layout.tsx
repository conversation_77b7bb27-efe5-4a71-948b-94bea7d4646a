import './globals.css'
import type { Metadata } from 'next'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import GoogleAdSense from '@/components/GoogleAdSense'

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://learnmarts.com'),
  title: {
    default: 'Anmody AI 教學部落 | OpenAI API ChatGPT 專業教學',
    template: '%s | Anmody AI 教學部落'
  },
  description: '專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學文章，助您掌握最新 AI 應用技術與開發技巧。',
  keywords: ['OpenAI', 'ChatGPT', 'AI教學', '部落格', 'API開發', '人工智慧', '機器學習', '程式教學'],
  authors: [{ name: 'Anmody.com' }],
  creator: 'Anmody.com',
  publisher: 'Anmody.com',
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_HK',
    url: 'https://learnmarts.com/',
    siteName: 'Anmody AI 教學部落',
    title: 'Anmody AI 教學部落 | OpenAI API ChatGPT 專業教學',
    description: '專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學文章，助您掌握最新 AI 應用技術與開發技巧。',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Anmody AI 教學部落',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Anmody AI 教學部落',
    description: '專業的 AI 技術教學部落，提供 OpenAI API、ChatGPT 深度教學文章',
    images: ['/images/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code', // 需要替换为实际的Google验证码
    // yandex: 'your-yandex-verification-code', // 如果需要Yandex
    // bing: 'your-bing-verification-code', // 如果需要Bing
  },
  // 添加更多SEO优化
  category: 'technology',
  alternates: {
    canonical: 'https://learnmarts.com',
    languages: {
      'zh-Hant': 'https://learnmarts.com',
      'zh-CN': 'https://learnmarts.com',
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-Hant-HK">
      <head>
        <link rel="canonical" href="https://learnmarts.com/" />
        <link rel="alternate" type="application/rss+xml" title="Anmody AI 教學部落 RSS" href="/rss.xml" />
        {/* 添加更多SEO优化标签 */}
        <meta name="theme-color" content="#2563eb" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Anmody AI 教學" />
        <link rel="apple-touch-icon" href="/favicon.ico" />
        <link rel="manifest" href="/manifest.json" />
        <script src="/js/tag-redirect.js" defer></script>
      </head>
      <body className="min-h-screen bg-gray-50 flex flex-col">
        <GoogleAdSense />
        <Header />
        <div className="flex-grow">
          <main className="container mx-auto px-4 py-8 max-w-7xl">
            {children}
          </main>
        </div>
        <Footer />
      </body>
    </html>
  )
}