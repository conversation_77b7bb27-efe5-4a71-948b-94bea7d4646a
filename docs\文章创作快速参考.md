# 📝 文章创作快速参考

## 🚀 快速开始

### 1. 创建文章文件
```bash
# 在 data/articles/ 目录下创建
touch data/articles/your-article-name.md
```

### 2. 文章模板
```markdown
---
title: 文章标题
date: 2025-07-01T00:00:00.000Z
excerpt: 文章摘要描述
category: 教學
tags:
  - 标签1
  - 标签2
article_id: 下一个可用ID
author: Anmody
readTime: X 分鐘閱讀
tags_slug:
  - tag1-slug
  - tag2-slug
category_slug: tutorials
---

# 文章标题

文章内容...
```

### 3. 构建和预览
```bash
# 完整构建
npm run build

# 本地预览
npx serve@latest out
```

## 📋 字段速查

| 字段 | 必填 | 示例 |
|------|------|------|
| title | ✅ | "Docker 部署指南" |
| date | ✅ | 2025-07-01T00:00:00.000Z |
| excerpt | ✅ | "学习 Docker 容器化部署" |
| category | ✅ | "教學" |
| tags | ✅ | ["Docker", "DevOps"] |
| article_id | ✅ | 8 |
| author | ❌ | "Anmody" |
| readTime | ❌ | "5 分鐘閱讀" |

## 🏷️ 分类和标签

### 现有分类
- **教學** (tutorials) - 技术教程
- **用戶指南** (user-guide-cat) - 产品使用说明  
- **站務公告** (site-announcements) - 网站公告

### 常用标签
**技术类**：OpenAI, ChatGPT, Docker, Kubernetes, JavaScript, Python
**类型类**：快速入門, 使用指南, 最佳實踐, 故障排除

## ✍️ 格式化技巧

### 标题结构
```markdown
# 主标题 (H1)
## 🚀 二级标题 (H2) 
### 三级标题 (H3)
```

### 代码块
```markdown
```javascript
const example = "指定语言";
```
```

### 信息框
```markdown
> **💡 小贴士**：有用的提示信息
> **⚠️ 注意**：重要注意事项
> **🚨 警告**：警告信息
```

### 表格
```markdown
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 值1 | 值2 | 值3 |
```

### 列表
```markdown
- ✅ 推荐做法
- ❌ 避免做法  
- 💡 小贴士
```

## 🔧 常用命令

```bash
# 获取下一个文章ID
grep -r "article_id:" data/articles/ | sort -n

# 生成文章数据
npm run generate-articles-data

# 启动开发服务器
npm run dev

# 完整构建
npm run build

# 本地预览构建结果
npx serve@latest out
```

## ✅ 发布检查清单

- [ ] Front matter 格式正确
- [ ] article_id 唯一
- [ ] 标题包含关键词
- [ ] 摘要描述准确
- [ ] 代码示例可运行
- [ ] 本地构建成功
- [ ] 文章页面正常显示

## 🎯 内容建议

### 文章结构
1. **引言** - 介绍主题和学习目标
2. **前置需求** - 所需基础知识
3. **正文内容** - 分步骤详细说明
4. **最佳实践** - 实用建议
5. **故障排除** - 常见问题解决
6. **总结** - 要点回顾
7. **相关资源** - 延伸阅读

### 写作技巧
- 使用繁体中文（香港）
- 专业但友好的语调
- 清晰的标题层次
- 1500-3000 字长度
- 5-10 分钟阅读时间

## 🚨 常见错误

### YAML 格式错误
```yaml
# ❌ 错误
tags:
- Docker
- DevOps

# ✅ 正确  
tags:
  - Docker
  - DevOps
```

### 文章ID 冲突
```bash
# 检查现有ID
grep "article_id:" data/articles/*.md
```

### 分类不匹配
```markdown
# 确保 category 和 category_slug 匹配
category: 教學
category_slug: tutorials
```

## 📞 获取帮助

1. **构建错误** - 检查终端错误信息
2. **格式问题** - 参考现有文章格式
3. **技术问题** - 查看项目文档

---

💡 **提示**：详细指南请参考 `docs/创建文章的说明.md`
