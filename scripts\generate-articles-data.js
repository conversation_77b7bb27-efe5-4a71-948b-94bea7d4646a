const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

const articlesDirectory = path.join(process.cwd(), 'data/articles');
const outputPath = path.join(process.cwd(), 'public/articles-data.json');

function generateArticlesData() {
  try {
    const files = fs.readdirSync(articlesDirectory);
    
    const articles = files
      .filter(name => name.endsWith('.md'))
      .map(name => {
        const fullPath = path.join(articlesDirectory, name);
        const fileContents = fs.readFileSync(fullPath, 'utf8');
        const { data, content } = matter(fileContents);
        
        return {
          slug: name.replace(/\.md$/, ''),
          title: data.title || 'Untitled',
          excerpt: data.excerpt || content.substring(0, 200).replace(/\n/g, ' ').trim() + '...',
          date: data.date || new Date().toISOString(),
          category: data.category || 'General',
          tags: data.tags || [],
          content: content,
          author: data.author || 'Admin',
          readTime: data.readTime || '5 min read'
        };
      })
      .sort((a, b) => new Date(b.date) - new Date(a.date)); // Sort by date, newest first

    fs.writeFileSync(outputPath, JSON.stringify(articles, null, 2));
    console.log(`✅ Articles data generated: ${articles.length} articles exported to public/articles-data.json`);
    
    return articles;
  } catch (error) {
    console.error('❌ Error generating articles data:', error);
    process.exit(1);
  }
}

// If this file is run directly
if (require.main === module) {
  generateArticlesData();
}

module.exports = generateArticlesData;
